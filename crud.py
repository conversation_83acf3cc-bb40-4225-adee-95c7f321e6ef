from datetime import datetime, timedelta
from typing import Optional, Dict
from sqlalchemy.orm import Session
from models import Task, Project, Merchant, User, Organization  # 添加Organization模型导入
from schemas import TaskCreate, TaskUpdate, UserCreate, ProjectCreate, ProjectUpdate, OrganizationCreate, OrganizationUpdate  # 添加Organization相关的导入
from logger_config import db_logger, error_logger, log_database_operation, log_error


# Organization CRUD operations
def get_organization(db: Session, organization_id: str):
    """获取单个组织"""
    return db.query(Organization).filter(Organization.id == organization_id, Organization.deleted == False).first()

def get_organizations(db: Session, skip: int = 0, limit: int = 100):
    """获取组织列表"""
    return db.query(Organization).filter(Organization.deleted == False).offset(skip).limit(limit).all()

def create_organization(db: Session, organization: OrganizationCreate):
    """创建组织"""
    try:
        db_organization = Organization(**organization.model_dump())
        db.add(db_organization)
        db.commit()
        db.refresh(db_organization)

        log_database_operation("CREATE", "organizations", str(db_organization.id), "system")
        return db_organization
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "create_organization", "organization_data": organization.model_dump()})
        raise

def update_organization(db: Session, organization_id: str, organization: OrganizationUpdate):
    """更新组织"""
    try:
        db_organization = get_organization(db, organization_id)
        if db_organization:
            for key, value in organization.model_dump(exclude_unset=True).items():
                setattr(db_organization, key, value)
            db.commit()
            db.refresh(db_organization)

            log_database_operation("UPDATE", "organizations", organization_id, "system")
            return db_organization
        return None
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "update_organization", "organization_id": organization_id})
        raise

def delete_organization(db: Session, organization_id: str):
    """删除组织（软删除）"""
    try:
        db_organization = get_organization(db, organization_id)
        if db_organization:
            setattr(db_organization, 'deleted', True)
            db.commit()

            log_database_operation("DELETE", "organizations", organization_id, "system")
            return True
        return False
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "delete_organization", "organization_id": organization_id})
        raise

def get_users_by_organization(db: Session, organization_id: str, skip: int = 0, limit: int = 100):
    """获取组织下的用户列表"""
    return db.query(User).filter(
        User.organization_id == organization_id,
        User.deleted == False
    ).offset(skip).limit(limit).all()

def get_projects_by_organization(db: Session, organization_id: str, skip: int = 0, limit: int = 100):
    """获取组织下的项目列表"""
    return db.query(Project).filter(
        Project.organization_id == organization_id
    ).offset(skip).limit(limit).all()


def get_merchant(db: Session, merchant_id: int):
    return db.query(Merchant).filter(Merchant.id == merchant_id).first()


def get_tasks(db: Session, skip: int = 0, limit: int = 100, project_id: Optional[str] = None):
    query = db.query(Task)
    if project_id:
        query = query.filter(Task.project_id == project_id)
    return query.offset(skip).limit(limit).all()

def get_task(db: Session, task_id: str):
    return db.query(Task).filter(Task.id == task_id).first()

def create_task(db: Session, task: TaskCreate):
    try:
        db_task = Task(**task.model_dump())
        db.add(db_task)
        db.commit()
        db.refresh(db_task)

        log_database_operation("CREATE", "tasks", str(db_task.id), task.owner_id or "unknown")
        return db_task
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "create_task", "task_data": task.model_dump()})
        raise

def update_task(task_id: str, task: TaskUpdate, db: Session):  # 修改参数类型为TaskUpdate
    db_task = db.query(Task).filter(Task.id == task_id).first()
    if db_task:
        for key, value in task.model_dump().items():
            setattr(db_task, key, value)
        db.commit()
        db.refresh(db_task)
        return db_task
    return None


def delete_task(db: Session, task_id: str):
    db_task = db.query(Task).filter(Task.id == task_id).first()
    if db_task:
        db.delete(db_task)
        db.commit()
        return db_task
    return None

# 项目相关的CRUD函数
def get_project(db: Session, project_id: str):
    return db.query(Project).filter(Project.id == project_id).first()

def get_projects(db: Session, skip: int = 0, limit: int = 100):
    return db.query(Project).offset(skip).limit(limit).all()

def create_project(db: Session, project: ProjectCreate):
    db_project = Project(**project.model_dump())
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    return db_project

def update_project(project_id: str, project: ProjectUpdate, db: Session):
    db_project = db.query(Project).filter(Project.id == project_id).first()
    if db_project:
        for key, value in project.model_dump().items():
            setattr(db_project, key, value)
        db.commit()
        db.refresh(db_project)
        return db_project
    return None

def delete_project(db: Session, project_id: str):
    db_project = db.query(Project).filter(Project.id == project_id).first()
    if db_project:
        db.delete(db_project)
        db.commit()
        return True
    return False

def get_task_stats(db: Session, merchant_id: int, time_range: str, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict:
    # 这里实现统计逻辑，根据时间范围查询任务数据
    query = db.query(Task).join(Project).filter(Project.merchant_id == merchant_id)
    
    if time_range == "today":
        today = datetime.today().date()
        query = query.filter(Task.end_date >= today, Task.end_date <= today + timedelta(days=1))
    elif time_range == "week":
        today = datetime.today().date()
        week_start = today - timedelta(days=today.weekday())
        query = query.filter(Task.end_date >= week_start, Task.end_date <= today + timedelta(days=7))
    elif time_range == "month":
        today = datetime.today().date()
        month_start = today.replace(day=1)
        next_month = (today.replace(day=28) + timedelta(days=4)).replace(day=1)
        query = query.filter(Task.end_date >= month_start, Task.end_date <= next_month - timedelta(days=1))
    elif time_range == "custom" and start_date and end_date:
        query = query.filter(Task.end_date >= start_date, Task.end_date <= end_date)
    
    tasks = query.all()
    
    total_tasks = len(tasks)
    # 使用更安全的方式处理可能为None的progress字段
    completed_tasks = 0
    for task in tasks:
        progress = getattr(task, 'progress', None)
        if progress is not None and float(progress) >= 99.99:
            completed_tasks += 1
            
    completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    return {
        "total_tasks": total_tasks,
        "completed_tasks": completed_tasks,
        "completion_rate": round(completion_rate, 2),
        "time_range": time_range
    }

def get_user(db: Session, user_id: str):
    return db.query(User).filter(User.id == user_id).first()

def get_users(db: Session, skip: int = 0, limit: int = 100):
    return db.query(User).offset(skip).limit(limit).all()

def get_users_with_filters(
    db: Session,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    role: Optional[str] = None,
    status: Optional[str] = None,
    department: Optional[str] = None
):
    """
    获取用户列表，支持分页、搜索和筛选
    """
    # 使用joinedload预加载组织信息，以便获取部门名称
    from sqlalchemy.orm import joinedload
    query = db.query(User).options(joinedload(User.organization)).filter(User.deleted == False)

    # 搜索功能：在姓名、邮箱、用户名中搜索
    if search:
        from sqlalchemy import or_
        search_pattern = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_pattern),
                User.name.ilike(search_pattern),
                User.email.ilike(search_pattern)
            )
        )

    # 角色筛选
    if role:
        query = query.filter(User.role == role)

    # 状态筛选
    if status:
        # 将前端的active/inactive转换为数据库的多种可能值
        if status == "active":
            # 活跃状态可能是：启用、1、active等
            query = query.filter(User.status.in_(["启用", "1", "active"]))
        elif status == "inactive":
            # 非活跃状态可能是：禁用、0、inactive等
            query = query.filter(User.status.in_(["禁用", "0", "inactive"]))

    # 部门筛选（通过organization_id）
    if department:
        query = query.filter(User.organization_id == department)

    # 计算总数
    total = query.count()

    # 分页
    offset = (page - 1) * page_size
    users = query.offset(offset).limit(page_size).all()

    # 计算分页信息
    total_pages = (total + page_size - 1) // page_size

    return {
        "items": users,
        "pagination": {
            "page": page,
            "pageSize": page_size,
            "total": total,
            "totalPages": total_pages,
            "hasNext": page < total_pages,
            "hasPrev": page > 1
        }
    }

def create_user(db: Session, user: UserCreate):
    try:
        # 密码加密处理
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

        user_data = user.model_dump()
        user_data['password'] = pwd_context.hash(user_data['password'])

        # 处理部门名称转换为organization_id
        if 'department' in user_data:
            department_name = user_data.pop('department')
            if department_name:
                # 根据部门名称查找组织ID
                organization = db.query(Organization).filter(
                    Organization.name == department_name,
                    Organization.deleted == False
                ).first()
                if organization:
                    user_data['organization_id'] = organization.id
                else:
                    # 如果部门不存在，创建新部门
                    new_org = Organization(
                        name=department_name,
                        description=f"自动创建的部门: {department_name}",
                        status="启用"
                    )
                    db.add(new_org)
                    db.flush()  # 获取ID但不提交
                    user_data['organization_id'] = new_org.id
            else:
                user_data['organization_id'] = None

        # 设置默认状态为启用（如果没有指定）
        if 'status' not in user_data or not user_data['status']:
            user_data['status'] = "启用"

        db_user = User(**user_data)
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        log_database_operation("CREATE", "users", str(db_user.id), str(db_user.id))
        return db_user
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "create_user", "username": user.username})
        raise

def update_user(user_id: str, user: UserCreate, db: Session):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

        user_data = user.model_dump()
        # 如果更新密码，需要加密
        if 'password' in user_data:
            user_data['password'] = pwd_context.hash(user_data['password'])

        for key, value in user_data.items():
            setattr(db_user, key, value)
        db.commit()
        db.refresh(db_user)
        return db_user
    return None

def update_user_info(user_id: str, user_update, db: Session):
    """更新用户信息的新函数，支持UserUpdate schema"""
    try:
        db_user = db.query(User).filter(User.id == user_id, User.deleted == False).first()
        if not db_user:
            return None

        # 获取更新数据，排除None值
        update_data = user_update.model_dump(exclude_unset=True, exclude_none=True)

        # 处理部门名称转换为organization_id
        if 'department' in update_data:
            department_name = update_data.pop('department')
            if department_name:
                # 根据部门名称查找组织ID
                organization = db.query(Organization).filter(
                    Organization.name == department_name,
                    Organization.deleted == False
                ).first()
                if organization:
                    update_data['organization_id'] = organization.id
                else:
                    # 如果部门不存在，可以选择创建或返回错误
                    # 这里选择设置为None
                    update_data['organization_id'] = None
            else:
                update_data['organization_id'] = None

        # 处理密码加密
        if 'password' in update_data and update_data['password']:
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            update_data['password'] = pwd_context.hash(update_data['password'])

        # 更新用户信息
        for key, value in update_data.items():
            if hasattr(db_user, key):
                setattr(db_user, key, value)

        db.commit()
        db.refresh(db_user)

        log_database_operation("UPDATE", "users", user_id, "system")
        return db_user

    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "update_user_info", "user_id": user_id})
        raise

def delete_user(db: Session, user_id: str):
    """软删除用户"""
    try:
        db_user = db.query(User).filter(User.id == user_id, User.deleted == False).first()
        if not db_user:
            return None

        # 软删除：设置deleted标志为True
        setattr(db_user, 'deleted', True)
        db.commit()
        db.refresh(db_user)

        log_database_operation("DELETE", "users", user_id, "system")
        return db_user
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "delete_user", "user_id": user_id})
        raise

def update_user_status(db: Session, user_id: str, status: str):
    """更新单个用户状态"""
    try:
        db_user = db.query(User).filter(User.id == user_id, User.deleted == False).first()
        if not db_user:
            return None

        # 将前端状态转换为数据库状态
        db_status = "启用" if status == "active" else "禁用"
        setattr(db_user, 'status', db_status)
        db.commit()
        db.refresh(db_user)

        log_database_operation("UPDATE", "users", user_id, "system")
        return db_user
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "update_user_status", "user_id": user_id, "status": status})
        raise

def batch_update_user_status(db: Session, user_ids: list, status: str):
    """批量更新用户状态"""
    try:
        # 将前端状态转换为数据库状态
        db_status = "启用" if status == "active" else "禁用"

        # 查询要更新的用户
        users = db.query(User).filter(
            User.id.in_(user_ids),
            User.deleted == False
        ).all()

        if not users:
            return []

        # 批量更新状态
        updated_count = db.query(User).filter(
            User.id.in_(user_ids),
            User.deleted == False
        ).update({"status": db_status}, synchronize_session=False)

        db.commit()

        # 记录操作日志
        for user_id in user_ids:
            log_database_operation("UPDATE", "users", user_id, "system")

        return {
            "updated_count": updated_count,
            "updated_users": [user.id for user in users],
            "status": status
        }
    except Exception as e:
        db.rollback()
        log_error(e, {"operation": "batch_update_user_status", "user_ids": user_ids, "status": status})
        raise

def activate_users(db: Session, user_ids: list):
    """激活用户"""
    return batch_update_user_status(db, user_ids, "active")

def deactivate_users(db: Session, user_ids: list):
    """禁用用户"""
    return batch_update_user_status(db, user_ids, "inactive")

# 删除重复的authenticate_user函数，使用models.py中的版本
