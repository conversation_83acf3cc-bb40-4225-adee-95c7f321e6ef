#!/usr/bin/env python3
"""
调试分页查询问题
"""

from database import get_db
from models import User, Organization
from crud import get_users_with_filters
import requests
import json

BASE_URL = "http://localhost:8000"

def check_database_directly():
    """直接检查数据库中的数据"""
    print("=== 直接检查数据库 ===")
    
    db = next(get_db())
    try:
        # 检查总用户数
        total_users = db.query(User).count()
        print(f"数据库中总用户数: {total_users}")
        
        # 检查未删除的用户数
        active_users = db.query(User).filter(User.deleted == False).count()
        print(f"未删除的用户数: {active_users}")
        
        # 获取前5个用户
        users = db.query(User).filter(User.deleted == False).limit(5).all()
        print(f"前5个用户:")
        for user in users:
            print(f"  - ID: {user.id}, 用户名: {user.username}, 姓名: {user.name}, 删除状态: {user.deleted}")
        
        # 检查组织数据
        orgs = db.query(Organization).limit(3).all()
        print(f"\n组织数据:")
        for org in orgs:
            print(f"  - ID: {org.id}, 名称: {org.name}")
            
    except Exception as e:
        print(f"❌ 数据库查询异常: {e}")
    finally:
        db.close()

def test_crud_function():
    """测试CRUD函数"""
    print("\n=== 测试CRUD函数 ===")
    
    db = next(get_db())
    try:
        # 测试基本分页查询
        result = get_users_with_filters(db, page=1, page_size=10)
        print(f"CRUD函数返回:")
        print(f"  用户数量: {len(result['items'])}")
        print(f"  分页信息: {result['pagination']}")
        
        if result['items']:
            print(f"  第一个用户: {result['items'][0].username}")
        
    except Exception as e:
        print(f"❌ CRUD函数异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def test_api_call():
    """测试API调用"""
    print("\n=== 测试API调用 ===")
    
    try:
        # 测试基本API调用
        response = requests.get(f"{BASE_URL}/users/list?page=1&pageSize=10")
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"API错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")

def test_different_page_sizes():
    """测试不同的分页大小"""
    print("\n=== 测试不同分页大小 ===")
    
    page_sizes = [5, 10, 20, 50]
    
    for page_size in page_sizes:
        try:
            response = requests.get(f"{BASE_URL}/users/list?page=1&pageSize={page_size}")
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    data = result.get("data", {})
                    items = data.get("items", [])
                    pagination = data.get("pagination", {})
                    print(f"页大小 {page_size}: 返回 {len(items)} 个用户, 总数: {pagination.get('total', 0)}")
                else:
                    print(f"页大小 {page_size}: API错误 - {result.get('msg')}")
            else:
                print(f"页大小 {page_size}: HTTP错误 {response.status_code}")
        except Exception as e:
            print(f"页大小 {page_size}: 异常 - {e}")

def test_without_pagination():
    """测试不使用分页的查询"""
    print("\n=== 测试不使用分页 ===")
    
    db = next(get_db())
    try:
        # 直接查询所有用户
        users = db.query(User).filter(User.deleted == False).all()
        print(f"直接查询返回用户数: {len(users)}")
        
        if users:
            print("用户列表:")
            for i, user in enumerate(users[:5]):  # 只显示前5个
                print(f"  {i+1}. {user.username} - {user.name} - {user.status}")
                
    except Exception as e:
        print(f"❌ 直接查询异常: {e}")
    finally:
        db.close()

def test_sql_query():
    """测试原始SQL查询"""
    print("\n=== 测试原始SQL查询 ===")
    
    db = next(get_db())
    try:
        # 执行原始SQL
        result = db.execute("SELECT COUNT(*) as count FROM users WHERE deleted = 0")
        count = result.fetchone()[0]
        print(f"SQL查询用户总数: {count}")
        
        result = db.execute("SELECT id, username, name, status, deleted FROM users WHERE deleted = 0 LIMIT 5")
        rows = result.fetchall()
        print(f"SQL查询前5个用户:")
        for row in rows:
            print(f"  - ID: {row[0]}, 用户名: {row[1]}, 姓名: {row[2]}, 状态: {row[3]}, 删除: {row[4]}")
            
    except Exception as e:
        print(f"❌ SQL查询异常: {e}")
    finally:
        db.close()

def main():
    """主函数"""
    print("开始调试分页查询问题...")
    
    # 1. 检查数据库数据
    check_database_directly()
    
    # 2. 测试CRUD函数
    test_crud_function()
    
    # 3. 测试API调用
    test_api_call()
    
    # 4. 测试不同分页大小
    test_different_page_sizes()
    
    # 5. 测试不使用分页
    test_without_pagination()
    
    # 6. 测试原始SQL
    test_sql_query()
    
    print("\n=== 调试完成 ===")

if __name__ == "__main__":
    main()
