from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.orm import Session
from schemas import DetailResponse, UserCreate, UserUpdate, LoginCredentials, UserListResponse, UserStatusUpdate, UserActivateRequest, UserDeactivateRequest
from crud import get_users_with_filters, create_user, update_user_info, delete_user as crud_delete_user, get_merchant, update_user_status
from models import authenticate_user
from database import get_db
import crud

# 配置JWT
SECRET_KEY = "your-secret-key"  # 在生产环境中应该使用安全的密钥
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_current_merchant(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        merchant_id = payload.get("sub")
        if merchant_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
        
    merchant = crud.get_merchant(db, merchant_id=int(merchant_id))
    if merchant is None:
        raise credentials_exception
    return merchant

router = APIRouter(
    prefix="/users",
    tags=["users"],
)

@router.delete("/{user_id}")
def delete_user_route(user_id: str, db: Session = Depends(get_db)):
    return crud_delete_user(db, user_id)

@router.get("/list", response_model=UserListResponse)
def read_users(
    page: int = 1,
    pageSize: int = 20,
    search: Optional[str] = None,
    role: Optional[str] = None,
    status: Optional[str] = None,
    department: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取用户列表，支持分页、搜索和筛选
    - page: 页码，默认1
    - pageSize: 每页数量，默认20
    - search: 搜索关键词（用户名）
    - role: 角色筛选
    - status: 状态筛选（active/inactive）
    - department: 部门筛选（组织ID）
    """
    result = get_users_with_filters(
        db=db,
        page=page,
        page_size=pageSize,
        search=search,
        role=role,
        status=status,
        department=department
    )

    # 转换用户数据为安全字典格式（脱敏敏感信息）
    user_data = [user.to_safe_dict() for user in result["items"]]

    # 构建响应数据
    response_data = {
        "items": user_data,
        "pagination": result["pagination"]
    }

    return UserListResponse(code=200, msg="获取用户列表成功", data=response_data)

@router.post("/create", response_model=DetailResponse)
def create_user_route(user: UserCreate, db: Session = Depends(get_db)):
    try:
        result = create_user(db, user)
        # 如果create_user已返回DetailResponse则直接返回，否则封装
        if isinstance(result, DetailResponse):
            return result
        return DetailResponse(code=200, msg="用户创建成功", data=result)
    except Exception as e:
        return DetailResponse(code=500, msg=f"用户创建失败: {str(e)}", data=None)

@router.put("/{user_id}", response_model=DetailResponse)
def update_user_route(user_id: str, user_update: UserUpdate, db: Session = Depends(get_db)):
    """
    更新用户信息
    - user_id: 用户ID
    - 支持的字段: username, name, email, phone, role, department, password
    """
    try:
        updated_user = update_user_info(user_id, user_update, db)
        if not updated_user:
            return DetailResponse(code=404, msg="用户不存在", data=None)

        return DetailResponse(
            code=200,
            msg="用户信息更新成功",
            data=updated_user.to_safe_dict()
        )
    except Exception as e:
        return DetailResponse(code=500, msg=f"更新用户信息失败: {str(e)}", data=None)

@router.put("/{user_id}/status", response_model=DetailResponse)
def update_user_status_route(user_id: str, status_update: UserStatusUpdate, db: Session = Depends(get_db)):
    """
    更新单个用户状态
    - user_id: 用户ID
    - status: active 或 inactive
    """
    try:
        updated_user = update_user_status(db, user_id, status_update.status)
        if not updated_user:
            return DetailResponse(code=404, msg="用户不存在", data=None)

        return DetailResponse(
            code=200,
            msg=f"用户状态已更新为{'激活' if status_update.status == 'active' else '禁用'}",
            data=updated_user.to_safe_dict()
        )
    except Exception as e:
        return DetailResponse(code=500, msg=f"更新用户状态失败: {str(e)}", data=None)

@router.post("/activate", response_model=DetailResponse)
def activate_users_route(request: UserActivateRequest, db: Session = Depends(get_db)):
    """
    批量激活用户
    - user_ids: 用户ID列表
    """
    try:
        result = crud.activate_users(db, request.user_ids)
        updated_count = result.get('updated_count', 0) if isinstance(result, dict) else 0
        return DetailResponse(
            code=200,
            msg=f"成功激活 {updated_count} 个用户",
            data=result
        )
    except Exception as e:
        return DetailResponse(code=500, msg=f"批量激活用户失败: {str(e)}", data=None)

@router.post("/deactivate", response_model=DetailResponse)
def deactivate_users_route(request: UserDeactivateRequest, db: Session = Depends(get_db)):
    """
    批量禁用用户
    - user_ids: 用户ID列表
    """
    try:
        result = deactivate_users(db, request.user_ids)
        updated_count = result.get('updated_count', 0) if isinstance(result, dict) else 0
        return DetailResponse(
            code=200,
            msg=f"成功禁用 {updated_count} 个用户",
            data=result
        )
    except Exception as e:
        return DetailResponse(code=500, msg=f"批量禁用用户失败: {str(e)}", data=None)

@router.post("/login", response_model=DetailResponse)
def login(credentials: LoginCredentials, db: Session = Depends(get_db)):
    # 执行认证
    result = authenticate_user(db, username=credentials.username, password=credentials.password)
    # 转换为Pydantic模型格式
    return DetailResponse(code=result.code, msg=result.msg, data=result.data)

@router.post("/logout", response_model=DetailResponse)
def logout(token: str = Depends(oauth2_scheme)):
    """
    用户退出登录
    - 验证token有效性
    - 由于使用的是无状态的JWT认证，退出登录只需前端清除token即可
    """
    try:
        # 验证token（可选，确保是有效用户发起的退出请求）
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if username is None:
            return DetailResponse(code=401, msg="无效的认证凭证", data=None)

        # 退出成功
        return DetailResponse(code=200, msg="成功登出", data={"username": username})

    except JWTError:
        # 即使token无效，也允许退出（前端清除token）
        return DetailResponse(code=200, msg="成功登出", data=None)