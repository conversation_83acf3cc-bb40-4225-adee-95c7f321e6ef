# 用户列表API文档

## 接口概述

用户列表接口已升级，支持分页、搜索和多维度筛选功能。

## 接口信息

- **URL**: `GET /users/list`
- **描述**: 获取用户列表，支持分页、搜索和筛选
- **响应模型**: `UserListResponse`

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，从1开始 |
| pageSize | int | 否 | 20 | 每页数量，最大建议100 |
| search | string | 否 | - | 搜索关键词（在用户名中搜索） |
| role | string | 否 | - | 角色筛选（如：admin、manager、member等） |
| status | string | 否 | - | 状态筛选（active/inactive） |
| department | string | 否 | - | 部门筛选（组织ID） |

## 请求示例

### 基本查询
```http
GET /users/list
```

### 分页查询
```http
GET /users/list?page=1&pageSize=10
```

### 搜索用户
```http
GET /users/list?search=admin&page=1&pageSize=20
```

### 角色筛选
```http
GET /users/list?role=admin&page=1&pageSize=20
```

### 状态筛选
```http
GET /users/list?status=active&page=1&pageSize=20
```

### 组合筛选
```http
GET /users/list?role=admin&status=active&search=test&page=1&pageSize=10
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": "17524878585172870",
        "name": "管理员",
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin",
        "department": "测试部门",
        "phone": "13800138000",
        "status": "active",
        "created_at": "2025-07-14 18:10:58",
        "organization_id": "O17524878576473295",
        "deleted": false,
        "updated_at": "2025-07-14 18:10:58"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 5,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 响应字段说明

#### 用户对象字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | string | 用户唯一标识 |
| name | string | 姓名 |
| username | string | 用户名 |
| email | string | 邮箱 |
| role | string | 角色 |
| department | string | 部门名称 |
| phone | string | 电话 |
| status | string | 状态（active/inactive） |
| created_at | string | 创建时间 |
| organization_id | string | 所属组织ID（用于筛选） |
| deleted | boolean | 是否已删除 |
| updated_at | string | 更新时间 |

#### 分页对象字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| page | int | 当前页码 |
| pageSize | int | 每页数量 |
| total | int | 总记录数 |
| totalPages | int | 总页数 |
| hasNext | boolean | 是否有下一页 |
| hasPrev | boolean | 是否有上一页 |

## 状态码说明

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 功能特性

### 1. 分页功能
- 支持自定义页码和每页数量
- 返回完整的分页信息
- 自动计算总页数和导航状态

### 2. 搜索功能
- 支持在姓名、用户名、邮箱中进行模糊搜索
- 搜索不区分大小写
- 可与其他筛选条件组合使用

### 3. 筛选功能
- **角色筛选**: 按用户角色精确筛选
- **状态筛选**: 按用户状态筛选（active/inactive）
- **部门筛选**: 按组织ID筛选用户
- 支持多个筛选条件同时使用

### 4. 状态标准化
- 数据库中的状态值（如"0"、"禁用"）会自动转换为标准格式
- 前端统一使用 active/inactive 状态值

## 使用建议

1. **分页大小**: 建议每页数量不超过100，以保证响应性能
2. **搜索优化**: 搜索关键词建议至少2个字符以提高查询效率
3. **组合查询**: 可以组合多个筛选条件实现精确查询
4. **状态筛选**: 使用标准的 active/inactive 值进行状态筛选

## 更新历史

- **v2.0**: 新增分页、搜索和筛选功能
- **v1.0**: 基础用户列表查询功能
