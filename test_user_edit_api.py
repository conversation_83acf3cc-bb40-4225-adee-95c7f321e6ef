#!/usr/bin/env python3
"""
测试用户编辑API接口
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def get_test_user():
    """获取一个测试用户"""
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                # 找一个非admin用户进行测试
                for user in users:
                    if user["username"] != "admin":
                        return user
        return None
    except Exception as e:
        print(f"❌ 获取测试用户失败: {e}")
        return None

def test_user_edit_with_your_format():
    """测试您提供的参数格式"""
    print("\n=== 测试用户编辑接口（您的参数格式） ===")
    
    test_user = get_test_user()
    if not test_user:
        print("❌ 没有找到测试用户")
        return
    
    user_id = test_user["id"]
    print(f"测试用户ID: {user_id}")
    print(f"原始用户信息: {test_user['name']} ({test_user['username']})")
    
    # 使用您提供的参数格式
    payload = {
        "username": "manager001",
        "email": "<EMAIL>",
        "name": "张经理",
        "role": "manager",
        "department": "测试部门",
        "phone": "13800138001",
        "password": "1234"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/users/{user_id}", json=payload)
        print(f"状态码: {response.status_code}")
        print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 用户编辑成功")
                user_data = result.get("data", {})
                print(f"  更新后姓名: {user_data.get('name')}")
                print(f"  更新后用户名: {user_data.get('username')}")
                print(f"  更新后邮箱: {user_data.get('email')}")
                print(f"  更新后角色: {user_data.get('role')}")
                print(f"  更新后部门: {user_data.get('department')}")
                print(f"  更新后电话: {user_data.get('phone')}")
            else:
                print(f"❌ 编辑失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_partial_user_edit():
    """测试部分字段更新"""
    print("\n=== 测试部分字段更新 ===")
    
    test_user = get_test_user()
    if not test_user:
        print("❌ 没有找到测试用户")
        return
    
    user_id = test_user["id"]
    
    # 只更新部分字段
    payload = {
        "name": "更新的姓名",
        "phone": "13999999999"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/users/{user_id}", json=payload)
        print(f"状态码: {response.status_code}")
        print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 部分字段更新成功")
                user_data = result.get("data", {})
                print(f"  更新后姓名: {user_data.get('name')}")
                print(f"  更新后电话: {user_data.get('phone')}")
                print(f"  用户名保持: {user_data.get('username')}")
            else:
                print(f"❌ 更新失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_password_update():
    """测试密码更新"""
    print("\n=== 测试密码更新 ===")
    
    test_user = get_test_user()
    if not test_user:
        print("❌ 没有找到测试用户")
        return
    
    user_id = test_user["id"]
    
    # 只更新密码
    payload = {
        "password": "new_password_123"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/users/{user_id}", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 密码更新成功")
                print("  注意: 密码已加密存储，不会在响应中显示")
            else:
                print(f"❌ 密码更新失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_invalid_user_edit():
    """测试无效用户编辑"""
    print("\n=== 测试无效用户编辑 ===")
    
    # 测试不存在的用户ID
    invalid_user_id = "invalid_user_id_123"
    payload = {
        "name": "测试姓名"
    }
    
    try:
        response = requests.put(f"{BASE_URL}/users/{invalid_user_id}", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 404:
                print("✅ 正确处理了不存在的用户")
                print(f"  错误信息: {result.get('msg')}")
            else:
                print(f"❌ 未正确处理不存在的用户: {result}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def verify_user_changes():
    """验证用户变更"""
    print("\n=== 验证用户变更 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                print("当前用户信息:")
                for user in users:
                    print(f"  - {user['name']} ({user['username']}) - {user['email']} - {user['role']} - {user['department']} - {user['phone']}")
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """运行所有测试"""
    print("开始测试用户编辑API...")
    
    # 显示初始状态
    verify_user_changes()
    
    # 测试您提供的参数格式
    test_user_edit_with_your_format()
    
    # 测试部分字段更新
    test_partial_user_edit()
    
    # 测试密码更新
    test_password_update()
    
    # 测试错误处理
    test_invalid_user_edit()
    
    # 显示最终状态
    verify_user_changes()
    
    print("\n=== 用户编辑API测试完成 ===")

if __name__ == "__main__":
    main()
