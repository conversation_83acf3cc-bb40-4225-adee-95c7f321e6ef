from pydantic import BaseModel
from typing import Optional, List, Any, Union
from datetime import date
from enum import Enum

# Organization schemas
class OrganizationBase(BaseModel):
    name: str
    description: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    status: Optional[str] = "启用"

class OrganizationCreate(OrganizationBase):
    pass

class Organization(OrganizationBase):
    id: str
    class Config:
        from_attributes = True

class OrganizationUpdate(OrganizationBase):
    pass


class UserBase(BaseModel):
    username: str
    name: Optional[str] = None  # 姓名
    email: Optional[str] = None  # 邮箱
    phone: Optional[str] = None  # 电话
    role: Optional[str] = None
    status: Optional[Union[str, int]] = "禁用"  # 兼容前端传int
    organization_id: Optional[str] = None  # 添加组织关联

class UserCreate(BaseModel):
    username: str
    name: Optional[str] = None  # 姓名
    email: Optional[str] = None  # 邮箱
    phone: Optional[str] = None  # 电话
    role: Optional[str] = None
    department: Optional[str] = None  # 部门名称（而不是organization_id）
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    name: Optional[str] = None  # 姓名
    email: Optional[str] = None  # 邮箱
    phone: Optional[str] = None  # 电话
    role: Optional[str] = None
    department: Optional[str] = None  # 部门名称（而不是organization_id）
    password: Optional[str] = None  # 密码可选，不传则不更新

class User(UserBase):
    id: str
    class Config:
        from_attributes = True

class LoginCredentials(BaseModel):
    username: str
    password: str

class UserListParams(BaseModel):
    page: Optional[int] = 1
    pageSize: Optional[int] = 20
    search: Optional[str] = None  # 搜索关键词（姓名、邮箱、用户名）
    role: Optional[str] = None    # 角色筛选：admin/manager/member
    status: Optional[str] = None  # 状态筛选：active/inactive
    department: Optional[str] = None  # 部门筛选

class UserListResponse(BaseModel):
    code: int
    msg: str
    data: dict  # 包含items和pagination信息

class UserStatusUpdate(BaseModel):
    status: str  # active 或 inactive

class UserActivateRequest(BaseModel):
    user_ids: List[str]  # 支持批量激活

class UserDeactivateRequest(BaseModel):
    user_ids: List[str]  # 支持批量禁用

# ProjectBase
class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    target: Optional[int] = None
    progress: Optional[float] = 0.0
    owner_id: Optional[str] = None
    organization_id: Optional[str] = None  # 添加组织关联

# ProjectCreate
class ProjectCreate(ProjectBase):
    pass

# Project
class Project(ProjectBase):
    id: str
    class Config:
        from_attributes = True

class ProjectUpdate(ProjectBase):
    pass


class TaskBase(BaseModel):
    project_id: str
    name: str
    priority: Optional[str] = None
    progress: Optional[float] = 0.0
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    owner_id: Optional[str] = None
    related_task: Optional[str] = None

class TaskCreate(TaskBase):
    pass

class Task(TaskBase):
    id: str
    class Config:
        from_attributes = True

class TaskUpdate(TaskBase):
    pass

class BaseResponse(BaseModel):
    code: int
    msg: str
    data: Any

class ListResponse(BaseResponse):
    data: List[Any]

class DetailResponse(BaseResponse):
    data: Any

class TimeRange(str, Enum):
    TODAY = "today"
    WEEK = "week"
    MONTH = "month"
    CUSTOM = "custom"

class TaskStatsResponse(BaseModel):
    total_count: int
    tasks: List[dict]