from pydantic import BaseModel
from typing import Optional, List, Any, Union
from datetime import date
from enum import Enum

# Organization schemas
class OrganizationBase(BaseModel):
    name: str
    description: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    status: Optional[str] = "启用"

class OrganizationCreate(OrganizationBase):
    pass

class Organization(OrganizationBase):
    id: str
    class Config:
        from_attributes = True

class OrganizationUpdate(OrganizationBase):
    pass


class UserBase(BaseModel):
    username: str
    role: Optional[str] = None
    status: Optional[Union[str, int]] = "禁用"  # 兼容前端传int
    organization_id: Optional[str] = None  # 添加组织关联

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: str
    class Config:
        from_attributes = True

class LoginCredentials(BaseModel):
    username: str
    password: str

class UserListParams(BaseModel):
    page: Optional[int] = 1
    pageSize: Optional[int] = 20
    search: Optional[str] = None  # 搜索关键词（姓名、邮箱、用户名）
    role: Optional[str] = None    # 角色筛选：admin/manager/member
    status: Optional[str] = None  # 状态筛选：active/inactive
    department: Optional[str] = None  # 部门筛选

class UserListResponse(BaseModel):
    code: int
    msg: str
    data: dict  # 包含items和pagination信息

# ProjectBase
class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    target: Optional[int] = None
    progress: Optional[float] = 0.0
    owner_id: Optional[str] = None
    organization_id: Optional[str] = None  # 添加组织关联

# ProjectCreate
class ProjectCreate(ProjectBase):
    pass

# Project
class Project(ProjectBase):
    id: str
    class Config:
        from_attributes = True

class ProjectUpdate(ProjectBase):
    pass


class TaskBase(BaseModel):
    project_id: str
    name: str
    priority: Optional[str] = None
    progress: Optional[float] = 0.0
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    owner_id: Optional[str] = None
    related_task: Optional[str] = None

class TaskCreate(TaskBase):
    pass

class Task(TaskBase):
    id: str
    class Config:
        from_attributes = True

class TaskUpdate(TaskBase):
    pass

class BaseResponse(BaseModel):
    code: int
    msg: str
    data: Any

class ListResponse(BaseResponse):
    data: List[Any]

class DetailResponse(BaseResponse):
    data: Any

class TimeRange(str, Enum):
    TODAY = "today"
    WEEK = "week"
    MONTH = "month"
    CUSTOM = "custom"

class TaskStatsResponse(BaseModel):
    total_count: int
    tasks: List[dict]