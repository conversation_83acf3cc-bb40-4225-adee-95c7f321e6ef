#!/usr/bin/env python3
"""
日志中间件
自动记录API请求和响应信息
"""

import time
import json
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from logger_config import api_logger, error_logger, performance_logger, logger_config


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志记录中间件"""
    
    def __init__(self, app, exclude_paths: list = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or ["/docs", "/redoc", "/openapi.json", "/favicon.ico"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录日志"""
        
        # 生成请求ID
        request_id = logger_config.get_request_id()
        
        # 获取客户端IP
        client_ip = self.get_client_ip(request)
        
        # 跳过不需要记录的路径
        if request.url.path in self.exclude_paths:
            return await call_next(request)
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        await self.log_request(request, request_id, client_ip)
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            await self.log_response(request, response, request_id, process_time)
            
            # 记录性能信息
            self.log_performance_metrics(request, process_time, response.status_code)
            
            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录错误
            await self.log_error(request, e, request_id, process_time)
            
            # 返回错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "code": 500,
                    "msg": "Internal Server Error",
                    "data": None,
                    "request_id": request_id
                },
                headers={"X-Request-ID": request_id}
            )
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 返回直连IP
        return request.client.host if request.client else "unknown"
    
    async def log_request(self, request: Request, request_id: str, client_ip: str):
        """记录请求信息"""
        try:
            # 获取请求体（如果存在）
            body = None
            if request.method in ["POST", "PUT", "PATCH"]:
                try:
                    body_bytes = await request.body()
                    if body_bytes:
                        body = body_bytes.decode('utf-8')
                        # 尝试解析JSON
                        try:
                            body = json.loads(body)
                        except json.JSONDecodeError:
                            pass
                except Exception:
                    body = "Unable to read body"
            
            # 获取查询参数
            query_params = dict(request.query_params)
            
            # 获取用户信息（如果存在）
            user_id = None
            auth_header = request.headers.get("Authorization")
            if auth_header:
                # 这里可以解析JWT token获取用户ID
                # 暂时记录有认证头
                user_id = "authenticated_user"
            
            log_data = {
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": query_params,
                "headers": dict(request.headers),
                "client_ip": client_ip,
                "user_agent": request.headers.get("User-Agent"),
                "user_id": user_id,
                "body": body
            }
            
            api_logger.info(f"Request started: {json.dumps(log_data, ensure_ascii=False, default=str)}")
            
        except Exception as e:
            api_logger.error(f"Error logging request: {str(e)}")
    
    async def log_response(self, request: Request, response: Response, request_id: str, process_time: float):
        """记录响应信息"""
        try:
            log_data = {
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "process_time_ms": round(process_time * 1000, 2),
                "response_headers": dict(response.headers)
            }
            
            # 记录响应体（仅对错误响应）
            if response.status_code >= 400:
                try:
                    # 这里可以根据需要记录响应体
                    log_data["response_body"] = "Error response"
                except Exception:
                    pass
            
            api_logger.info(f"Request completed: {json.dumps(log_data, ensure_ascii=False, default=str)}")
            
        except Exception as e:
            api_logger.error(f"Error logging response: {str(e)}")
    
    async def log_error(self, request: Request, error: Exception, request_id: str, process_time: float):
        """记录错误信息"""
        try:
            import traceback
            
            log_data = {
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "error_type": type(error).__name__,
                "error_message": str(error),
                "process_time_ms": round(process_time * 1000, 2),
                "traceback": traceback.format_exc()
            }
            
            error_logger.error(f"Request error: {json.dumps(log_data, ensure_ascii=False, default=str)}")
            
        except Exception as e:
            error_logger.error(f"Error logging error: {str(e)}")
    
    def log_performance_metrics(self, request: Request, process_time: float, status_code: int):
        """记录性能指标"""
        try:
            # 定义慢请求阈值（秒）
            slow_request_threshold = 2.0
            
            if process_time > slow_request_threshold:
                log_data = {
                    "method": request.method,
                    "path": request.url.path,
                    "process_time_ms": round(process_time * 1000, 2),
                    "status_code": status_code,
                    "type": "slow_request"
                }
                
                performance_logger.warning(f"Slow request detected: {json.dumps(log_data, ensure_ascii=False)}")
            
            # 记录所有请求的性能数据
            log_data = {
                "method": request.method,
                "path": request.url.path,
                "process_time_ms": round(process_time * 1000, 2),
                "status_code": status_code
            }
            
            performance_logger.info(f"Performance: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            performance_logger.error(f"Error logging performance: {str(e)}")


class RequestContextMiddleware(BaseHTTPMiddleware):
    """请求上下文中间件，用于在请求处理过程中传递请求ID"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """添加请求上下文"""
        request_id = logger_config.get_request_id()
        
        # 将请求ID添加到request state中
        request.state.request_id = request_id
        
        response = await call_next(request)
        return response
