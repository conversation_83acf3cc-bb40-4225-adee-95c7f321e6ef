#!/usr/bin/env python3
"""
验证total不受pageSize影响的逻辑
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_total_consistency():
    """测试total在不同pageSize下保持一致"""
    print("=== 验证total不受pageSize影响 ===")
    
    page_sizes = [1, 5, 10, 15, 20, 50, 100]
    results = []
    
    for page_size in page_sizes:
        try:
            response = requests.get(f"{BASE_URL}/users/list?page=1&page_size={page_size}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    data = result.get("data", {})
                    items = data.get("items", [])
                    pagination = data.get("pagination", {})
                    
                    total = pagination.get("total", 0)
                    returned_count = len(items)
                    total_pages = pagination.get("totalPages", 0)
                    
                    results.append({
                        "page_size": page_size,
                        "total": total,
                        "returned_count": returned_count,
                        "total_pages": total_pages,
                        "expected_returned": min(page_size, total)
                    })
                    
                    print(f"pageSize={page_size:3d} | total={total:2d} | 返回={returned_count:2d} | 总页数={total_pages:2d}")
                    
                    # 验证返回数量是否正确
                    expected_count = min(page_size, total)
                    if returned_count == expected_count:
                        print(f"  ✅ 返回数量正确")
                    else:
                        print(f"  ❌ 返回数量错误，期望{expected_count}，实际{returned_count}")
                        
                else:
                    print(f"pageSize={page_size}: API错误 - {result.get('msg')}")
            else:
                print(f"pageSize={page_size}: HTTP错误 {response.status_code}")
                
        except Exception as e:
            print(f"pageSize={page_size}: 异常 - {e}")
    
    # 分析total一致性
    if results:
        first_total = results[0]["total"]
        total_consistent = all(r["total"] == first_total for r in results)
        
        print(f"\n=== 分析结果 ===")
        if total_consistent:
            print(f"✅ total值在所有pageSize下保持一致: {first_total}")
        else:
            print(f"❌ total值不一致:")
            for r in results:
                print(f"  pageSize={r['page_size']}: total={r['total']}")
        
        # 验证分页计算
        print(f"\n=== 分页计算验证 ===")
        for r in results:
            expected_pages = (first_total + r['page_size'] - 1) // r['page_size']
            if r['total_pages'] == expected_pages:
                print(f"✅ pageSize={r['page_size']:3d}: 总页数计算正确 ({r['total_pages']})")
            else:
                print(f"❌ pageSize={r['page_size']:3d}: 总页数计算错误，期望{expected_pages}，实际{r['total_pages']}")

def test_with_filters():
    """测试带筛选条件时total的一致性"""
    print(f"\n=== 测试筛选条件下的total一致性 ===")
    
    test_cases = [
        {"params": "", "desc": "无筛选"},
        {"params": "&role=member", "desc": "角色筛选"},
        {"params": "&status=active", "desc": "状态筛选"},
        {"params": "&search=user", "desc": "搜索筛选"},
    ]
    
    page_sizes = [5, 10, 20]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['desc']} ---")
        params = test_case['params']
        totals = []
        
        for page_size in page_sizes:
            try:
                url = f"{BASE_URL}/users/list?page=1&page_size={page_size}{params}"
                response = requests.get(url)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        data = result.get("data", {})
                        pagination = data.get("pagination", {})
                        total = pagination.get("total", 0)
                        totals.append(total)
                        
                        print(f"  pageSize={page_size}: total={total}")
                    else:
                        print(f"  pageSize={page_size}: API错误 - {result.get('msg')}")
                else:
                    print(f"  pageSize={page_size}: HTTP错误 {response.status_code}")
                    
            except Exception as e:
                print(f"  pageSize={page_size}: 异常 - {e}")
        
        # 检查一致性
        if totals and all(t == totals[0] for t in totals):
            print(f"  ✅ {test_case['desc']}下total值一致: {totals[0]}")
        elif totals:
            print(f"  ❌ {test_case['desc']}下total值不一致: {totals}")

def test_pagination_math():
    """测试分页数学计算"""
    print(f"\n=== 测试分页数学计算 ===")
    
    # 获取总数据量
    response = requests.get(f"{BASE_URL}/users/list?page=1&page_size=1000")
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 200:
            total_records = result.get("data", {}).get("pagination", {}).get("total", 0)
            print(f"总数据量: {total_records}")
            
            # 测试不同pageSize的分页计算
            test_page_sizes = [1, 3, 5, 7, 10, 15, 20, 25]
            
            print(f"\n分页计算验证:")
            print(f"{'pageSize':<8} {'总页数':<6} {'期望页数':<8} {'最后页数量':<10} {'验证':<4}")
            print("-" * 50)
            
            for page_size in test_page_sizes:
                if page_size <= total_records:
                    # 计算期望的总页数
                    expected_total_pages = (total_records + page_size - 1) // page_size
                    
                    # 计算最后一页应该有多少条记录
                    expected_last_page_count = total_records % page_size
                    if expected_last_page_count == 0:
                        expected_last_page_count = page_size
                    
                    # 获取实际的分页信息
                    response = requests.get(f"{BASE_URL}/users/list?page=1&page_size={page_size}")
                    if response.status_code == 200:
                        result = response.json()
                        if result.get("code") == 200:
                            pagination = result.get("data", {}).get("pagination", {})
                            actual_total_pages = pagination.get("totalPages", 0)
                            
                            # 获取最后一页的数据
                            last_page_response = requests.get(f"{BASE_URL}/users/list?page={actual_total_pages}&page_size={page_size}")
                            if last_page_response.status_code == 200:
                                last_page_result = last_page_response.json()
                                if last_page_result.get("code") == 200:
                                    last_page_items = last_page_result.get("data", {}).get("items", [])
                                    actual_last_page_count = len(last_page_items)
                                    
                                    # 验证
                                    pages_correct = actual_total_pages == expected_total_pages
                                    last_page_correct = actual_last_page_count == expected_last_page_count
                                    
                                    status = "✅" if pages_correct and last_page_correct else "❌"
                                    
                                    print(f"{page_size:<8} {actual_total_pages:<6} {expected_total_pages:<8} {actual_last_page_count:<10} {status}")

def main():
    """主函数"""
    print("开始验证total逻辑...")
    
    # 测试total一致性
    test_total_consistency()
    
    # 测试筛选条件下的total
    test_with_filters()
    
    # 测试分页数学计算
    test_pagination_math()
    
    print("\n=== 验证完成 ===")

if __name__ == "__main__":
    main()
