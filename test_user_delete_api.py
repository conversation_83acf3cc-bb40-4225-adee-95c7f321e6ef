#!/usr/bin/env python3
"""
测试用户删除API接口
验证敏感信息脱敏和软删除功能
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def get_test_user():
    """获取一个测试用户"""
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                # 找一个非admin用户进行测试
                for user in users:
                    if user["username"] != "admin":
                        return user
        return None
    except Exception as e:
        print(f"❌ 获取测试用户失败: {e}")
        return None

def create_test_user_for_deletion():
    """创建一个专门用于删除测试的用户"""
    print("\n=== 创建测试用户 ===")
    
    payload = {
        "username": "delete_test_user",
        "name": "删除测试用户",
        "email": "<EMAIL>",
        "phone": "13800138999",
        "role": "member",
        "password": "test123456"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 测试用户创建成功")
                user_data = result.get("data", {})
                print(f"  用户ID: {user_data.get('id')}")
                print(f"  用户名: {user_data.get('username')}")
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_user_deletion():
    """测试用户删除功能"""
    print("\n=== 测试用户删除功能 ===")
    
    # 创建测试用户
    test_user_id = create_test_user_for_deletion()
    if not test_user_id:
        print("❌ 无法创建测试用户，跳过删除测试")
        return
    
    print(f"测试用户ID: {test_user_id}")
    
    try:
        response = requests.delete(f"{BASE_URL}/users/{test_user_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 用户删除成功")
                user_data = result.get("data", {})
                
                # 验证敏感信息脱敏
                print("\n=== 验证敏感信息脱敏 ===")
                email = user_data.get("email")
                phone = user_data.get("phone")
                
                print(f"  原始邮箱: <EMAIL>")
                print(f"  脱敏邮箱: {email}")
                print(f"  原始电话: 13800138999")
                print(f"  脱敏电话: {phone}")
                
                # 验证是否包含密码字段
                if "password" in user_data:
                    print("❌ 响应中包含密码字段！")
                else:
                    print("✅ 响应中不包含密码字段")
                
                # 验证删除状态
                if user_data.get("deleted"):
                    print("✅ 用户已标记为删除")
                else:
                    print("❌ 用户未标记为删除")
                    
            else:
                print(f"❌ 删除失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return test_user_id

def test_soft_delete_verification(deleted_user_id):
    """验证软删除功能"""
    print("\n=== 验证软删除功能 ===")
    
    if not deleted_user_id:
        print("❌ 没有已删除的用户ID")
        return
    
    # 1. 验证用户列表中不再显示已删除用户
    print("1. 检查用户列表")
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                deleted_user_found = any(user["id"] == deleted_user_id for user in users)
                
                if not deleted_user_found:
                    print("✅ 已删除用户不在用户列表中显示")
                else:
                    print("❌ 已删除用户仍在用户列表中显示")
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 2. 验证再次删除同一用户
    print("\n2. 测试重复删除")
    try:
        response = requests.delete(f"{BASE_URL}/users/{deleted_user_id}")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 404:
                print("✅ 正确处理了重复删除请求")
                print(f"  错误信息: {result.get('msg')}")
            else:
                print(f"❌ 未正确处理重复删除: {result}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_invalid_user_deletion():
    """测试删除不存在的用户"""
    print("\n=== 测试删除不存在的用户 ===")
    
    invalid_user_id = "invalid_user_id_123"
    
    try:
        response = requests.delete(f"{BASE_URL}/users/{invalid_user_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 404:
                print("✅ 正确处理了不存在的用户")
                print(f"  错误信息: {result.get('msg')}")
            else:
                print(f"❌ 未正确处理不存在的用户: {result}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_data_masking_in_user_list():
    """测试用户列表中的数据脱敏"""
    print("\n=== 测试用户列表中的数据脱敏 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                
                print("检查用户列表中的敏感信息脱敏:")
                for user in users[:2]:  # 只检查前两个用户
                    print(f"\n用户: {user.get('name')} ({user.get('username')})")
                    
                    # 检查邮箱脱敏
                    email = user.get("email")
                    if email and "*" in email:
                        print(f"  ✅ 邮箱已脱敏: {email}")
                    elif email:
                        print(f"  ❌ 邮箱未脱敏: {email}")
                    else:
                        print(f"  ⚠️  邮箱为空")
                    
                    # 检查电话脱敏
                    phone = user.get("phone")
                    if phone and "*" in phone:
                        print(f"  ✅ 电话已脱敏: {phone}")
                    elif phone:
                        print(f"  ❌ 电话未脱敏: {phone}")
                    else:
                        print(f"  ⚠️  电话为空")
                    
                    # 检查是否包含密码
                    if "password" in user:
                        print(f"  ❌ 包含密码字段！")
                    else:
                        print(f"  ✅ 不包含密码字段")
                        
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """运行所有测试"""
    print("开始测试用户删除API和敏感信息脱敏...")
    
    # 测试用户列表中的数据脱敏
    test_data_masking_in_user_list()
    
    # 测试用户删除功能
    deleted_user_id = test_user_deletion()
    
    # 验证软删除功能
    test_soft_delete_verification(deleted_user_id)
    
    # 测试错误处理
    test_invalid_user_deletion()
    
    print("\n=== 用户删除API和敏感信息脱敏测试完成 ===")

if __name__ == "__main__":
    main()
