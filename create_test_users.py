#!/usr/bin/env python3
"""
创建测试用户数据
"""

from database import get_db
from models import User, Organization
from sqlalchemy.orm import Session

def create_test_organization():
    """创建测试组织"""
    db = next(get_db())
    try:
        # 检查是否已存在测试组织
        existing_org = db.query(Organization).filter(Organization.name == "测试部门").first()
        if existing_org:
            print(f"测试组织已存在: {existing_org.id}")
            return existing_org.id
        
        # 创建新组织
        org = Organization(
            name="测试部门",
            description="用于测试的部门",
            status="启用"
        )
        db.add(org)
        db.commit()
        db.refresh(org)
        print(f"✅ 创建测试组织成功: {org.id}")
        return org.id
    except Exception as e:
        print(f"❌ 创建测试组织失败: {e}")
        db.rollback()
        return None
    finally:
        db.close()

def create_test_users():
    """创建测试用户"""
    db = next(get_db())
    try:
        # 创建测试组织
        org_id = create_test_organization()
        
        # 测试用户数据
        test_users = [
            {
                "username": "admin",
                "name": "管理员",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "role": "admin",
                "password": "admin123",
                "status": "启用",
                "organization_id": org_id
            },
            {
                "username": "manager001",
                "name": "张经理",
                "email": "<EMAIL>",
                "phone": "13800138001",
                "role": "manager",
                "password": "manager123",
                "status": "启用",
                "organization_id": org_id
            },
            {
                "username": "user001",
                "name": "李员工",
                "email": "<EMAIL>",
                "phone": "13800138002",
                "role": "member",
                "password": "user123",
                "status": "启用",
                "organization_id": org_id
            },
            {
                "username": "user002",
                "name": "王员工",
                "email": "<EMAIL>",
                "phone": "13800138003",
                "role": "member",
                "password": "user123",
                "status": "禁用",
                "organization_id": org_id
            },
            {
                "username": "test_user",
                "name": "测试用户",
                "email": "<EMAIL>",
                "phone": "13800138004",
                "role": "member",
                "password": "test123",
                "status": "启用",
                "organization_id": None  # 无部门用户
            }
        ]
        
        created_users = []
        
        for user_data in test_users:
            # 检查用户是否已存在
            existing_user = db.query(User).filter(User.username == user_data["username"]).first()
            if existing_user:
                print(f"用户 {user_data['username']} 已存在，跳过创建")
                continue
            
            # 密码加密
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            user_data['password'] = pwd_context.hash(user_data['password'])
            
            # 创建用户
            user = User(**user_data)
            db.add(user)
            db.commit()
            db.refresh(user)
            
            created_users.append(user)
            print(f"✅ 创建用户成功: {user.username} ({user.name})")
        
        print(f"\n总共创建了 {len(created_users)} 个用户")
        return created_users
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        db.rollback()
        return []
    finally:
        db.close()

def main():
    """主函数"""
    print("开始创建测试用户数据...")
    
    users = create_test_users()
    
    if users:
        print("\n=== 创建的用户列表 ===")
        for user in users:
            print(f"- {user.name} ({user.username}) - {user.role} - {user.status}")
    
    print("\n=== 测试数据创建完成 ===")

if __name__ == "__main__":
    main()
