#!/usr/bin/env python3
"""
数据库迁移脚本：添加组织相关字段
- 为 users 表添加 organization_id 字段
- 为 projects 表添加 organization_id 字段
- 创建 organizations 表
"""

from sqlalchemy import create_engine, text, Column, String, DateTime, Boolean
from sqlalchemy.orm import sessionmaker
from database import SQLALCHEMY_DATABASE_URL
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """执行数据库迁移"""
    engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
    
    with engine.connect() as conn:
        try:
            # 1. 检查并创建 organizations 表
            logger.info("检查 organizations 表...")
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='organizations'")).fetchone()
            
            if not result:
                logger.info("创建 organizations 表...")
                conn.execute(text("""
                    CREATE TABLE organizations (
                        id VARCHAR(50) PRIMARY KEY,
                        name VARCHAR(100) NOT NULL UNIQUE,
                        description VARCHAR(500),
                        address VARCHAR(200),
                        phone VARCHAR(20),
                        email VARCHAR(100),
                        website VARCHAR(200),
                        status VARCHAR(10) DEFAULT '启用',
                        created_at DATETIME,
                        updated_at DATETIME,
                        deleted BOOLEAN DEFAULT 0
                    )
                """))
                logger.info("organizations 表创建成功")
            else:
                logger.info("organizations 表已存在")
            
            # 2. 检查 users 表是否有 organization_id 字段
            logger.info("检查 users 表的 organization_id 字段...")
            users_columns = conn.execute(text("PRAGMA table_info(users)")).fetchall()
            users_column_names = [col[1] for col in users_columns]
            
            if 'organization_id' not in users_column_names:
                logger.info("为 users 表添加 organization_id 字段...")
                conn.execute(text("ALTER TABLE users ADD COLUMN organization_id VARCHAR(50)"))
                logger.info("users 表 organization_id 字段添加成功")
            else:
                logger.info("users 表已有 organization_id 字段")
            
            # 3. 检查 projects 表是否有 organization_id 字段
            logger.info("检查 projects 表的 organization_id 字段...")
            projects_columns = conn.execute(text("PRAGMA table_info(projects)")).fetchall()
            projects_column_names = [col[1] for col in projects_columns]
            
            if 'organization_id' not in projects_column_names:
                logger.info("为 projects 表添加 organization_id 字段...")
                conn.execute(text("ALTER TABLE projects ADD COLUMN organization_id VARCHAR(50)"))
                logger.info("projects 表 organization_id 字段添加成功")
            else:
                logger.info("projects 表已有 organization_id 字段")
            
            # 提交更改
            conn.commit()
            logger.info("数据库迁移完成！")
            
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            conn.rollback()
            raise

def check_table_structure():
    """检查表结构"""
    engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
    
    with engine.connect() as conn:
        # 检查所有表
        tables = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'")).fetchall()
        logger.info(f"数据库中的表: {[table[0] for table in tables]}")
        
        # 检查 users 表结构
        if any(table[0] == 'users' for table in tables):
            users_columns = conn.execute(text("PRAGMA table_info(users)")).fetchall()
            logger.info("users 表结构:")
            for col in users_columns:
                logger.info(f"  {col[1]} ({col[2]}) - nullable: {not col[3]}")
        
        # 检查 projects 表结构
        if any(table[0] == 'projects' for table in tables):
            projects_columns = conn.execute(text("PRAGMA table_info(projects)")).fetchall()
            logger.info("projects 表结构:")
            for col in projects_columns:
                logger.info(f"  {col[1]} ({col[2]}) - nullable: {not col[3]}")
        
        # 检查 organizations 表结构
        if any(table[0] == 'organizations' for table in tables):
            org_columns = conn.execute(text("PRAGMA table_info(organizations)")).fetchall()
            logger.info("organizations 表结构:")
            for col in org_columns:
                logger.info(f"  {col[1]} ({col[2]}) - nullable: {not col[3]}")

if __name__ == "__main__":
    logger.info("开始数据库迁移...")
    
    # 执行迁移
    migrate_database()
    
    # 检查结果
    logger.info("\n检查迁移结果:")
    check_table_structure()
    
    logger.info("迁移脚本执行完成！")
