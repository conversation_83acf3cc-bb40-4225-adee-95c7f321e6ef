#!/usr/bin/env python3
"""
日志配置模块
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class LoggerConfig:
    """日志配置类"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 日志级别映射
        self.level_mapping = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        # 默认配置
        self.default_config = {
            'level': 'INFO',
            'format': '%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S',
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5,
            'console_output': True,
            'file_output': True
        }
    
    def setup_logger(self, 
                    name: str, 
                    level: str = 'INFO',
                    log_file: Optional[str] = None,
                    console_output: bool = True,
                    file_output: bool = True) -> logging.Logger:
        """
        设置并返回配置好的logger
        
        Args:
            name: logger名称
            level: 日志级别
            log_file: 日志文件名（可选）
            console_output: 是否输出到控制台
            file_output: 是否输出到文件
            
        Returns:
            配置好的logger实例
        """
        logger = logging.getLogger(name)
        
        # 避免重复添加handler
        if logger.handlers:
            return logger
            
        logger.setLevel(self.level_mapping.get(level.upper(), logging.INFO))
        
        # 创建formatter
        formatter = logging.Formatter(
            fmt=self.default_config['format'],
            datefmt=self.default_config['date_format']
        )
        
        # 控制台输出
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(self.level_mapping.get(level.upper(), logging.INFO))
            logger.addHandler(console_handler)
        
        # 文件输出
        if file_output:
            if not log_file:
                log_file = f"{name}.log"
            
            file_path = self.log_dir / log_file
            
            # 使用RotatingFileHandler实现日志轮转
            file_handler = logging.handlers.RotatingFileHandler(
                filename=file_path,
                maxBytes=self.default_config['max_file_size'],
                backupCount=self.default_config['backup_count'],
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(self.level_mapping.get(level.upper(), logging.INFO))
            logger.addHandler(file_handler)
        
        return logger
    
    def setup_app_loggers(self) -> dict:
        """
        设置应用程序的所有logger
        
        Returns:
            包含所有logger的字典
        """
        loggers = {}
        
        # 主应用logger
        loggers['app'] = self.setup_logger(
            name='app',
            level='INFO',
            log_file='app.log'
        )
        
        # API请求logger
        loggers['api'] = self.setup_logger(
            name='api',
            level='INFO',
            log_file='api.log'
        )
        
        # 数据库操作logger
        loggers['database'] = self.setup_logger(
            name='database',
            level='INFO',
            log_file='database.log'
        )
        
        # 认证相关logger
        loggers['auth'] = self.setup_logger(
            name='auth',
            level='INFO',
            log_file='auth.log'
        )
        
        # 错误logger
        loggers['error'] = self.setup_logger(
            name='error',
            level='ERROR',
            log_file='error.log'
        )
        
        # 性能监控logger
        loggers['performance'] = self.setup_logger(
            name='performance',
            level='INFO',
            log_file='performance.log'
        )
        
        return loggers
    
    def get_request_id(self) -> str:
        """生成请求ID用于追踪"""
        from datetime import datetime
        import random
        return f"REQ{int(datetime.now().timestamp() * 1000)}{random.randint(1000, 9999)}"


# 全局日志配置实例
logger_config = LoggerConfig()

# 设置应用程序logger
app_loggers = logger_config.setup_app_loggers()

# 导出常用的logger
app_logger = app_loggers['app']
api_logger = app_loggers['api']
db_logger = app_loggers['database']
auth_logger = app_loggers['auth']
error_logger = app_loggers['error']
performance_logger = app_loggers['performance']


def log_function_call(func_name: str, args: dict = None, user_id: str = None, request_id: str = None):
    """记录函数调用日志"""
    log_data = {
        'function': func_name,
        'user_id': user_id,
        'request_id': request_id,
        'args': args or {}
    }
    api_logger.info(f"Function call: {log_data}")


def log_database_operation(operation: str, table: str, record_id: str = None, user_id: str = None):
    """记录数据库操作日志"""
    log_data = {
        'operation': operation,
        'table': table,
        'record_id': record_id,
        'user_id': user_id
    }
    db_logger.info(f"Database operation: {log_data}")


def log_auth_event(event: str, user_id: str = None, ip_address: str = None, success: bool = True):
    """记录认证事件日志"""
    log_data = {
        'event': event,
        'user_id': user_id,
        'ip_address': ip_address,
        'success': success
    }
    auth_logger.info(f"Auth event: {log_data}")


def log_error(error: Exception, context: dict = None, user_id: str = None, request_id: str = None):
    """记录错误日志"""
    import traceback
    log_data = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'context': context or {},
        'user_id': user_id,
        'request_id': request_id,
        'traceback': traceback.format_exc()
    }
    error_logger.error(f"Error occurred: {log_data}")


def log_performance(operation: str, duration: float, details: dict = None):
    """记录性能日志"""
    log_data = {
        'operation': operation,
        'duration_ms': round(duration * 1000, 2),
        'details': details or {}
    }
    performance_logger.info(f"Performance: {log_data}")


if __name__ == "__main__":
    # 测试日志配置
    print("测试日志配置...")
    
    app_logger.info("应用程序启动")
    api_logger.info("API调用测试")
    db_logger.info("数据库操作测试")
    auth_logger.info("认证事件测试")
    error_logger.error("错误日志测试")
    performance_logger.info("性能监控测试")
    
    print("日志测试完成，请检查logs目录下的日志文件")
