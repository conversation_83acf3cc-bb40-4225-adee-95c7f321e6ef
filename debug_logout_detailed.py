#!/usr/bin/env python3
"""
详细调试退出接口
"""
import urllib.request
import urllib.parse
import json
from jose import jwt

def debug_logout_detailed():
    """详细调试退出接口"""
    # 首先登录获取token
    login_url = "http://127.0.0.1:8000/users/login"
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    json_data = json.dumps(login_data).encode('utf-8')
    req = urllib.request.Request(login_url, data=json_data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        with urllib.request.urlopen(req) as response:
            response_data = response.read().decode('utf-8')
            login_response = json.loads(response_data)
            
            if login_response.get('code') == 200:
                token = login_response['data']['access_token']
                print(f"登录成功，获取到token: {token}")
                
                # 验证token是否可以被正确解码
                try:
                    SECRET_KEY = "your-secret-key"  # 与routers/users.py中的SECRET_KEY一致
                    ALGORITHM = "HS256"
                    
                    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                    print(f"Token解码成功: {json.dumps(payload, indent=2)}")
                    print(f"用户名: {payload.get('sub')}")
                    
                except Exception as e:
                    print(f"Token解码失败: {e}")
                    print("这可能是SECRET_KEY不匹配的原因")
                
                # 使用token调用退出接口
                logout_url = "http://127.0.0.1:8000/users/logout"
                logout_req = urllib.request.Request(logout_url, data=b'', method='POST')
                logout_req.add_header('Content-Type', 'application/json')
                logout_req.add_header('Authorization', f'Bearer {token}')
                
                print(f"\n发送退出请求到: {logout_url}")
                print(f"Authorization header: Bearer {token[:50]}...")
                
                with urllib.request.urlopen(logout_req) as logout_response:
                    logout_data = logout_response.read().decode('utf-8')
                    print(f"退出接口状态码: {logout_response.status}")
                    print(f"退出接口原始响应: {logout_data}")
                    
                    logout_json = json.loads(logout_data)
                    print(f"退出接口解析后响应: {json.dumps(logout_json, indent=2, ensure_ascii=False)}")
                    
                    # 分析结果
                    if logout_json.get('data') is None:
                        print("\n⚠️  注意：退出接口返回的data为null")
                        print("这表明JWT解码时可能出现了异常")
                        print("请检查SECRET_KEY是否一致")
                    else:
                        print(f"\n✅ 退出成功，用户名: {logout_json['data'].get('username')}")
            else:
                print(f"登录失败: {login_response}")
                
    except Exception as e:
        print(f"调试退出时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_logout_detailed()
