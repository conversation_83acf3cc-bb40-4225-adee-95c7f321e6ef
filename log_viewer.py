#!/usr/bin/env python3
"""
日志查看工具
提供日志文件的查看、搜索和分析功能
"""

import os
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional


class LogViewer:
    """日志查看器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        if not self.log_dir.exists():
            print(f"日志目录不存在: {log_dir}")
            return
    
    def list_log_files(self) -> List[Path]:
        """列出所有日志文件"""
        return list(self.log_dir.glob("*.log"))
    
    def read_log_file(self, file_path: Path, lines: int = 100) -> List[str]:
        """读取日志文件的最后N行"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if lines > 0 else all_lines
        except Exception as e:
            print(f"读取日志文件失败: {e}")
            return []
    
    def search_logs(self, keyword: str, log_type: str = None, hours: int = 24) -> List[Dict]:
        """搜索日志"""
        results = []
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        log_files = self.list_log_files()
        if log_type:
            log_files = [f for f in log_files if log_type in f.name]
        
        for log_file in log_files:
            lines = self.read_log_file(log_file, lines=0)  # 读取全部
            
            for line_num, line in enumerate(lines, 1):
                if keyword.lower() in line.lower():
                    # 尝试解析时间戳
                    try:
                        timestamp_str = line.split(' | ')[0]
                        log_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        
                        if log_time >= cutoff_time:
                            results.append({
                                'file': log_file.name,
                                'line_number': line_num,
                                'timestamp': timestamp_str,
                                'content': line.strip()
                            })
                    except:
                        # 如果无法解析时间戳，仍然包含这行
                        results.append({
                            'file': log_file.name,
                            'line_number': line_num,
                            'timestamp': 'unknown',
                            'content': line.strip()
                        })
        
        return results
    
    def get_error_summary(self, hours: int = 24) -> Dict:
        """获取错误摘要"""
        error_results = self.search_logs("ERROR", hours=hours)
        
        summary = {
            'total_errors': len(error_results),
            'error_types': {},
            'recent_errors': error_results[-10:] if error_results else []
        }
        
        for error in error_results:
            content = error['content']
            # 尝试提取错误类型
            if 'ERROR' in content:
                parts = content.split('ERROR')
                if len(parts) > 1:
                    error_msg = parts[1].strip()
                    error_type = error_msg.split(':')[0].strip() if ':' in error_msg else 'Unknown'
                    summary['error_types'][error_type] = summary['error_types'].get(error_type, 0) + 1
        
        return summary
    
    def get_api_stats(self, hours: int = 24) -> Dict:
        """获取API统计信息"""
        api_results = self.search_logs("Request completed", "api", hours=hours)
        
        stats = {
            'total_requests': len(api_results),
            'status_codes': {},
            'slow_requests': [],
            'endpoints': {}
        }
        
        for request in api_results:
            content = request['content']
            try:
                # 尝试解析JSON格式的日志
                if '{' in content and '}' in content:
                    json_part = content[content.find('{'):content.rfind('}')+1]
                    data = json.loads(json_part)
                    
                    # 统计状态码
                    status_code = data.get('status_code', 'unknown')
                    stats['status_codes'][str(status_code)] = stats['status_codes'].get(str(status_code), 0) + 1
                    
                    # 统计端点
                    path = data.get('path', 'unknown')
                    stats['endpoints'][path] = stats['endpoints'].get(path, 0) + 1
                    
                    # 检查慢请求（超过1秒）
                    process_time = data.get('process_time_ms', 0)
                    if process_time > 1000:
                        stats['slow_requests'].append({
                            'path': path,
                            'method': data.get('method', 'unknown'),
                            'process_time_ms': process_time,
                            'timestamp': request['timestamp']
                        })
            except:
                pass
        
        return stats
    
    def display_summary(self):
        """显示日志摘要"""
        print("=== 日志文件摘要 ===")
        log_files = self.list_log_files()
        
        for log_file in log_files:
            file_size = log_file.stat().st_size
            file_size_mb = file_size / (1024 * 1024)
            mod_time = datetime.fromtimestamp(log_file.stat().st_mtime)
            
            print(f"文件: {log_file.name}")
            print(f"  大小: {file_size_mb:.2f} MB")
            print(f"  修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
    
    def tail_log(self, log_type: str = "app", lines: int = 20):
        """实时查看日志尾部"""
        log_file = self.log_dir / f"{log_type}.log"
        if not log_file.exists():
            print(f"日志文件不存在: {log_file}")
            return
        
        print(f"=== {log_type}.log 最后 {lines} 行 ===")
        recent_lines = self.read_log_file(log_file, lines)
        
        for line in recent_lines:
            print(line.rstrip())


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="日志查看工具")
    parser.add_argument("--log-dir", default="logs", help="日志目录路径")
    parser.add_argument("--search", help="搜索关键词")
    parser.add_argument("--log-type", help="日志类型 (app, api, database, auth, error, performance)")
    parser.add_argument("--hours", type=int, default=24, help="搜索最近N小时的日志")
    parser.add_argument("--tail", help="查看指定类型日志的尾部")
    parser.add_argument("--lines", type=int, default=20, help="显示行数")
    parser.add_argument("--summary", action="store_true", help="显示日志摘要")
    parser.add_argument("--errors", action="store_true", help="显示错误摘要")
    parser.add_argument("--api-stats", action="store_true", help="显示API统计")
    
    args = parser.parse_args()
    
    viewer = LogViewer(args.log_dir)
    
    if args.summary:
        viewer.display_summary()
    elif args.errors:
        error_summary = viewer.get_error_summary(args.hours)
        print("=== 错误摘要 ===")
        print(f"总错误数: {error_summary['total_errors']}")
        print("\n错误类型分布:")
        for error_type, count in error_summary['error_types'].items():
            print(f"  {error_type}: {count}")
        print("\n最近错误:")
        for error in error_summary['recent_errors']:
            print(f"  [{error['timestamp']}] {error['content']}")
    elif args.api_stats:
        api_stats = viewer.get_api_stats(args.hours)
        print("=== API统计 ===")
        print(f"总请求数: {api_stats['total_requests']}")
        print("\n状态码分布:")
        for code, count in api_stats['status_codes'].items():
            print(f"  {code}: {count}")
        print("\n端点访问统计:")
        for endpoint, count in sorted(api_stats['endpoints'].items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {endpoint}: {count}")
        if api_stats['slow_requests']:
            print(f"\n慢请求 (>{1000}ms):")
            for req in api_stats['slow_requests'][:5]:
                print(f"  [{req['timestamp']}] {req['method']} {req['path']} - {req['process_time_ms']}ms")
    elif args.search:
        results = viewer.search_logs(args.search, args.log_type, args.hours)
        print(f"=== 搜索结果: '{args.search}' ===")
        print(f"找到 {len(results)} 条记录")
        for result in results[-20:]:  # 显示最后20条
            print(f"[{result['file']}:{result['line_number']}] {result['content']}")
    elif args.tail:
        viewer.tail_log(args.tail, args.lines)
    else:
        print("请指定操作参数，使用 --help 查看帮助")


if __name__ == "__main__":
    main()
