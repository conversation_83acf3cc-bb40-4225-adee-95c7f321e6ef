#!/usr/bin/env python3
"""
测试组织管理CRUD操作
验证组织的创建、查询、更新、删除功能以及与用户、项目的关联关系
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database import SessionLocal, init_db
from models import Organization, User, Project
from schemas import OrganizationCreate, OrganizationUpdate, UserCreate, ProjectCreate
import crud

def test_organization_crud():
    """测试组织CRUD操作"""
    print("=== 测试组织管理CRUD操作 ===")
    
    # 初始化数据库
    init_db()
    
    # 创建数据库会话
    db: Session = SessionLocal()
    
    try:
        # 1. 测试创建组织
        print("\n=== 1. 创建组织 ===")
        test_org_data = OrganizationCreate(
            name="测试科技有限公司",
            description="这是一个测试组织，用于验证组织管理功能",
            address="北京市朝阳区测试大街123号",
            phone="010-12345678",
            email="<EMAIL>",
            website="https://www.testorg.com",
            status="启用"
        )
        
        created_org = crud.create_organization(db, test_org_data)
        print(f"✅ 组织创建成功")
        print(f"  组织ID: {created_org.id}")
        print(f"  组织名称: {created_org.name}")
        print(f"  组织状态: {created_org.status}")
        
        organization_id = created_org.id
        
        # 2. 测试查询单个组织
        print(f"\n=== 2. 查询组织详情 ===")
        retrieved_org = crud.get_organization(db, organization_id)
        if retrieved_org:
            print(f"✅ 组织查询成功")
            print(f"  组织ID: {retrieved_org.id}")
            print(f"  组织名称: {retrieved_org.name}")
            print(f"  组织描述: {retrieved_org.description}")
            print(f"  联系电话: {retrieved_org.phone}")
            print(f"  电子邮箱: {retrieved_org.email}")
        else:
            print("❌ 组织查询失败")
            
        # 3. 测试更新组织
        print(f"\n=== 3. 更新组织信息 ===")
        update_data = OrganizationUpdate(
            name="更新后的测试科技有限公司",
            description="这是更新后的组织描述",
            phone="010-87654321",
            status="启用"
        )
        
        updated_org = crud.update_organization(db, organization_id, update_data)
        if updated_org:
            print(f"✅ 组织更新成功")
            print(f"  新名称: {updated_org.name}")
            print(f"  新描述: {updated_org.description}")
            print(f"  新电话: {updated_org.phone}")
        else:
            print("❌ 组织更新失败")
            
        # 4. 测试组织列表查询
        print(f"\n=== 4. 查询组织列表 ===")
        organizations = crud.get_organizations(db, skip=0, limit=10)
        print(f"✅ 查询到 {len(organizations)} 个组织")
        for i, org in enumerate(organizations[:3], 1):  # 只显示前3个
            print(f"  组织{i}: {org.id} - {org.name}")
            
        # 5. 测试创建关联用户
        print(f"\n=== 5. 创建关联用户 ===")
        test_user_data = UserCreate(
            username="test_org_user",
            password="test123",
            role="员工",
            organization_id=organization_id  # 关联到组织
        )
        
        created_user = crud.create_user(db, test_user_data)
        print(f"✅ 用户创建成功")
        print(f"  用户ID: {created_user.id}")
        print(f"  用户名: {created_user.username}")
        print(f"  所属组织: {created_user.organization_id}")
        
        # 6. 测试创建关联项目
        print(f"\n=== 6. 创建关联项目 ===")
        test_project_data = ProjectCreate(
            name="组织测试项目",
            description="这是一个测试项目，关联到测试组织",
            owner_id=created_user.id,
            organization_id=organization_id  # 关联到组织
        )
        
        created_project = crud.create_project(db, test_project_data)
        print(f"✅ 项目创建成功")
        print(f"  项目ID: {created_project.id}")
        print(f"  项目名称: {created_project.name}")
        print(f"  所属组织: {created_project.organization_id}")
        
        # 7. 测试查询组织下的用户
        print(f"\n=== 7. 查询组织下的用户 ===")
        org_users = crud.get_users_by_organization(db, organization_id)
        print(f"✅ 查询到 {len(org_users)} 个用户")
        for user in org_users:
            print(f"  用户: {user.id} - {user.username}")
            
        # 8. 测试查询组织下的项目
        print(f"\n=== 8. 查询组织下的项目 ===")
        org_projects = crud.get_projects_by_organization(db, organization_id)
        print(f"✅ 查询到 {len(org_projects)} 个项目")
        for project in org_projects:
            print(f"  项目: {project.id} - {project.name}")
            
        # 9. 测试组织to_dict方法
        print(f"\n=== 9. 测试组织数据序列化 ===")
        org_dict = retrieved_org.to_dict()
        print(f"✅ 组织数据序列化成功")
        print(f"  序列化数据: {org_dict}")
        
        # 10. 测试删除组织（软删除）
        print(f"\n=== 10. 删除组织 ===")
        delete_success = crud.delete_organization(db, organization_id)
        if delete_success:
            print(f"✅ 组织删除成功")
            
            # 验证软删除
            deleted_org = crud.get_organization(db, organization_id)
            if deleted_org is None:
                print(f"✅ 软删除验证成功，已删除的组织无法查询到")
            else:
                print(f"❌ 软删除验证失败")
        else:
            print("❌ 组织删除失败")
            
        print(f"\n=== 组织管理CRUD测试完成 ===")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def test_organization_validation():
    """测试组织数据验证"""
    print("\n=== 测试组织数据验证 ===")
    
    try:
        # 测试必填字段验证
        print("1. 测试必填字段验证...")
        try:
            invalid_org = OrganizationCreate(
                description="缺少名称的组织"
            )
            print("❌ 必填字段验证失败")
        except Exception as e:
            print("✅ 必填字段验证成功")
            
        # 测试有效数据
        print("2. 测试有效数据...")
        valid_org = OrganizationCreate(
            name="有效组织",
            description="这是一个有效的组织"
        )
        print("✅ 有效数据验证成功")
        
    except Exception as e:
        print(f"❌ 数据验证测试失败: {str(e)}")

if __name__ == "__main__":
    print("开始组织管理功能测试...")
    test_organization_crud()
    test_organization_validation()
    print("组织管理功能测试完成！")
