#!/usr/bin/env python3
"""
简单的用户创建测试
"""

import requests
import json
import random

BASE_URL = "http://localhost:8000"

def test_create_user_with_your_format():
    """测试您提供的参数格式"""
    print("=== 测试用户创建接口（您的参数格式） ===")
    
    # 生成随机用户名和邮箱避免重复
    random_num = random.randint(1000, 9999)
    
    payload = {
        "username": f"user{random_num}",
        "email": f"test{random_num}@example.com",
        "name": "测试用户",
        "role": "member",
        "department": "测试部门",
        "phone": "15519057432",
        "password": "123123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 用户创建成功")
                user_data = result.get("data", {})
                
                print(f"\n=== 创建的用户信息 ===")
                print(f"  用户ID: {user_data.get('id')}")
                print(f"  姓名: {user_data.get('name')}")
                print(f"  用户名: {user_data.get('username')}")
                print(f"  邮箱: {user_data.get('email')}")
                print(f"  角色: {user_data.get('role')}")
                print(f"  部门: {user_data.get('department')}")
                print(f"  电话: {user_data.get('phone')}")
                print(f"  状态: {user_data.get('status')}")
                
                # 验证敏感信息脱敏
                print(f"\n=== 验证安全性 ===")
                if "password" in user_data:
                    print("❌ 响应中包含密码字段！")
                else:
                    print("✅ 响应中不包含密码字段")
                
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_duplicate_email():
    """测试重复邮箱的错误处理"""
    print("\n=== 测试重复邮箱错误处理 ===")
    
    payload = {
        "username": "test_duplicate_email",
        "email": "<EMAIL>",  # 使用已存在的邮箱
        "name": "重复邮箱测试",
        "role": "member",
        "department": "测试部门",
        "phone": "15519057432",
        "password": "123123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 400:
                print("✅ 正确处理了重复邮箱")
                print(f"  错误信息: {result.get('msg')}")
            else:
                print(f"❌ 未正确处理重复邮箱: {result}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def verify_user_list():
    """验证用户列表"""
    print("\n=== 验证用户列表 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                print(f"当前用户总数: {len(users)}")
                
                # 显示最新创建的几个用户
                print("\n最新用户:")
                for user in users[-3:]:
                    print(f"  - {user['name']} ({user['username']}) - {user['role']} - {user['department']} - {user['status']}")
                    
                    # 验证敏感信息脱敏
                    email = user.get("email", "")
                    phone = user.get("phone", "")
                    if "*" in email:
                        print(f"    ✅ 邮箱已脱敏: {email}")
                    elif email:
                        print(f"    ⚠️  邮箱未脱敏: {email}")
                    
                    if "*" in phone:
                        print(f"    ✅ 电话已脱敏: {phone}")
                    elif phone:
                        print(f"    ⚠️  电话未脱敏: {phone}")
                        
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("开始测试用户创建API...")
    
    # 测试用户创建
    user_id = test_create_user_with_your_format()
    
    # 测试错误处理
    test_duplicate_email()
    
    # 验证用户列表
    verify_user_list()
    
    print(f"\n=== 测试完成 ===")
    if user_id:
        print(f"成功创建用户ID: {user_id}")

if __name__ == "__main__":
    main()
