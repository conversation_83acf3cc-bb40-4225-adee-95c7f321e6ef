#!/usr/bin/env python3
"""
测试分页查询中total是否受pageSize影响
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_different_page_sizes():
    """测试不同pageSize对total的影响"""
    print("=== 测试不同pageSize对total的影响 ===")
    
    page_sizes = [5, 10, 15, 20, 50, 100]
    results = []
    
    for page_size in page_sizes:
        try:
            response = requests.get(f"{BASE_URL}/users/list?page=1&pageSize={page_size}")
            print(f"\n测试 pageSize={page_size}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    data = result.get("data", {})
                    items = data.get("items", [])
                    pagination = data.get("pagination", {})
                    
                    total = pagination.get("total", 0)
                    returned_count = len(items)
                    total_pages = pagination.get("totalPages", 0)
                    
                    results.append({
                        "pageSize": page_size,
                        "total": total,
                        "returned_count": returned_count,
                        "total_pages": total_pages
                    })
                    
                    print(f"  总数据量(total): {total}")
                    print(f"  返回数量: {returned_count}")
                    print(f"  总页数: {total_pages}")
                    print(f"  期望返回数量: {min(page_size, total)}")
                    
                    # 验证返回数量是否正确
                    expected_count = min(page_size, total)
                    if returned_count == expected_count:
                        print(f"  ✅ 返回数量正确")
                    else:
                        print(f"  ❌ 返回数量错误，期望{expected_count}，实际{returned_count}")
                        
                else:
                    print(f"  ❌ API错误: {result.get('msg')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    # 分析结果
    print(f"\n=== 结果分析 ===")
    if results:
        first_total = results[0]["total"]
        total_consistent = all(r["total"] == first_total for r in results)
        
        if total_consistent:
            print(f"✅ total值保持一致: {first_total}")
        else:
            print(f"❌ total值不一致:")
            for r in results:
                print(f"  pageSize={r['pageSize']}: total={r['total']}")
        
        print(f"\n详细结果:")
        for r in results:
            print(f"  pageSize={r['pageSize']:3d} | total={r['total']:2d} | 返回={r['returned_count']:2d} | 总页数={r['total_pages']:2d}")

def test_different_pages_same_size():
    """测试相同pageSize不同page的total"""
    print(f"\n=== 测试相同pageSize不同page的total ===")
    
    page_size = 5
    pages = [1, 2, 3, 4]
    results = []
    
    for page in pages:
        try:
            response = requests.get(f"{BASE_URL}/users/list?page={page}&pageSize={page_size}")
            print(f"\n测试 page={page}, pageSize={page_size}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    data = result.get("data", {})
                    items = data.get("items", [])
                    pagination = data.get("pagination", {})
                    
                    total = pagination.get("total", 0)
                    returned_count = len(items)
                    
                    results.append({
                        "page": page,
                        "total": total,
                        "returned_count": returned_count
                    })
                    
                    print(f"  总数据量(total): {total}")
                    print(f"  返回数量: {returned_count}")
                    
                else:
                    print(f"  ❌ API错误: {result.get('msg')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
    
    # 分析结果
    if results:
        first_total = results[0]["total"]
        total_consistent = all(r["total"] == first_total for r in results)
        
        if total_consistent:
            print(f"\n✅ 不同页面的total值保持一致: {first_total}")
        else:
            print(f"\n❌ 不同页面的total值不一致:")
            for r in results:
                print(f"  page={r['page']}: total={r['total']}")

def test_with_filters():
    """测试带筛选条件时的total"""
    print(f"\n=== 测试带筛选条件时的total ===")
    
    test_cases = [
        {"params": "", "desc": "无筛选"},
        {"params": "&role=member", "desc": "角色筛选"},
        {"params": "&status=active", "desc": "状态筛选"},
        {"params": "&search=user", "desc": "搜索筛选"},
    ]
    
    page_sizes = [5, 10, 20]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['desc']} ---")
        params = test_case['params']
        totals = []
        
        for page_size in page_sizes:
            try:
                url = f"{BASE_URL}/users/list?page=1&pageSize={page_size}{params}"
                response = requests.get(url)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        data = result.get("data", {})
                        pagination = data.get("pagination", {})
                        total = pagination.get("total", 0)
                        totals.append(total)
                        
                        print(f"  pageSize={page_size}: total={total}")
                    else:
                        print(f"  pageSize={page_size}: API错误 - {result.get('msg')}")
                else:
                    print(f"  pageSize={page_size}: HTTP错误 {response.status_code}")
                    
            except Exception as e:
                print(f"  pageSize={page_size}: 异常 - {e}")
        
        # 检查一致性
        if totals and all(t == totals[0] for t in totals):
            print(f"  ✅ {test_case['desc']}下total值一致: {totals[0]}")
        elif totals:
            print(f"  ❌ {test_case['desc']}下total值不一致: {totals}")

def main():
    """主函数"""
    print("开始测试分页查询中total是否受pageSize影响...")
    
    # 测试不同pageSize
    test_different_page_sizes()
    
    # 测试不同page
    test_different_pages_same_size()
    
    # 测试带筛选条件
    test_with_filters()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
