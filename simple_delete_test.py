#!/usr/bin/env python3
"""
简单的删除用户测试
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_user_list():
    """测试用户列表"""
    print("=== 测试用户列表 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                print(f"用户数量: {len(users)}")
                
                for user in users[:2]:  # 只显示前两个用户
                    print(f"\n用户: {user.get('name')} ({user.get('username')})")
                    print(f"  邮箱: {user.get('email')}")
                    print(f"  电话: {user.get('phone')}")
                    print(f"  是否包含密码: {'password' in user}")
                    
                return users
            else:
                print(f"❌ 获取失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return []

def test_create_user():
    """创建测试用户"""
    print("\n=== 创建测试用户 ===")
    
    payload = {
        "username": "delete_test",
        "name": "删除测试",
        "email": "<EMAIL>",
        "phone": "13999999999",
        "role": "member",
        "password": "test123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户创建成功")
                user_data = result.get("data", {})
                user_id = user_data.get("id")
                print(f"  用户ID: {user_id}")
                return user_id
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_delete_user(user_id):
    """测试删除用户"""
    print(f"\n=== 测试删除用户 (ID: {user_id}) ===")
    
    if not user_id:
        print("❌ 用户ID为空")
        return
    
    try:
        response = requests.delete(f"{BASE_URL}/users/{user_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 用户删除成功")
                user_data = result.get("data", {})
                
                # 检查敏感信息脱敏
                email = user_data.get("email")
                phone = user_data.get("phone")
                
                print(f"  脱敏邮箱: {email}")
                print(f"  脱敏电话: {phone}")
                print(f"  包含密码: {'password' in user_data}")
                print(f"  删除状态: {user_data.get('deleted')}")
                
            else:
                print(f"❌ 删除失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("开始简单删除测试...")
    
    # 1. 查看用户列表
    users = test_user_list()
    
    # 2. 创建测试用户
    test_user_id = test_create_user()
    
    # 3. 删除测试用户
    test_delete_user(test_user_id)
    
    # 4. 再次查看用户列表
    print("\n=== 删除后的用户列表 ===")
    test_user_list()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
