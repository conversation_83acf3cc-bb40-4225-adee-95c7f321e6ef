import random
from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, DateTime, Date, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float
from sqlalchemy.orm import Session, relationship
import datetime
from typing import Optional
from database import Base
import jwt  # Added jwt import
from passlib.context import CryptContext
# 从datetime导入timedelta
from datetime import timedelta, datetime as dt

detail_response_code = "detail_response_code"

class DetailResponse:
    def __init__(self, code: int, msg: str, data: Optional[dict]):
        self.code = code
        self.msg = msg
        self.data = data

# 创建密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = dt.utcnow() + expires_delta
        to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, "your-secret-key", algorithm="HS256")
    return encoded_jwt

def authenticate_user(db: Session, username: str, password: str):
    user = db.query(User).filter(User.username == username).first()  # 修改为username
    if not user or not pwd_context.verify(password, str(user.password)):
        return DetailResponse(code=401, msg="账户密码不可用", data=None)
    
    # 创建JWT token
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": user.username, "id": user.id},  # 修改为username
        expires_delta=access_token_expires
    )
    
    # 返回包含token的响应
    user_data = user.to_dict()
    user_data["access_token"] = access_token
    user_data["token_type"] = "bearer"
    return DetailResponse(code=200, msg="登陆成功", data=user_data)


class Organization(Base):
    __tablename__ = "organizations"

    # 组织ID：O前缀+时间戳+随机数生成，确保时间戳精确到毫秒
    id = Column(String(50), primary_key=True, default=lambda: f"O{int(dt.now().timestamp() * 1000)}{random.randint(1000, 9999)}")

    name = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(String(500))
    address = Column(String(200))
    phone = Column(String(20))
    email = Column(String(100))
    website = Column(String(200))
    status = Column(String(10), default="启用")  # 启用/禁用
    created_at = Column(DateTime, default=dt.now)
    updated_at = Column(DateTime, default=dt.now, onupdate=dt.now)
    deleted = Column(Boolean, default=False)

    # 关系映射
    users = relationship("User", back_populates="organization")
    projects = relationship("Project", back_populates="organization")

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "address": self.address,
            "phone": self.phone,
            "email": self.email,
            "website": self.website,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None,
            "deleted": self.deleted
        }


class User(Base):
    __tablename__ = "users"
    id = Column(String(50), primary_key=True, default=lambda: f"{int(dt.now().timestamp() * 1000)}{random.randint(1000, 9999)}")
    username = Column(String(50), unique=True, index=True)
    name = Column(String(100), nullable=True)  # 姓名
    email = Column(String(100), unique=True, index=True, nullable=True)  # 邮箱
    phone = Column(String(20), nullable=True)  # 电话
    role = Column(String(50))
    password = Column(String(100))
    status = Column(String(10), default="禁用")
    organization_id = Column(String(50), ForeignKey("organizations.id"), nullable=True)  # 关联组织
    created_at = Column(DateTime, default=dt.now)
    updated_at = Column(DateTime, default=dt.now, onupdate=dt.now)
    deleted = Column(Boolean, default=False)

    # 关系映射
    organization = relationship("Organization", back_populates="users")
    projects = relationship("Project", back_populates="owner")
    tasks = relationship("Task", back_populates="owner")

    def to_dict(self):
        def fmt(dt):
            if dt is None:
                return None
            return dt.strftime('%Y-%m-%d %H:%M:%S') if hasattr(dt, 'strftime') else str(dt)

        # 标准化状态显示
        def normalize_status(status):
            if status in ["启用", "1", "active"]:
                return "active"
            elif status in ["禁用", "0", "inactive"]:
                return "inactive"
            else:
                return status

        # 获取部门名称
        department_name = None
        if self.organization and hasattr(self.organization, 'name'):
            department_name = self.organization.name

        return {
            "id": self.id,
            "name": self.name,  # 姓名
            "username": self.username,  # 用户名
            "email": self.email,  # 邮箱
            "role": self.role,  # 角色
            "department": department_name,  # 部门名称
            "phone": self.phone,  # 电话
            "status": normalize_status(self.status),  # 状态
            "created_at": fmt(self.created_at),  # 创建时间
            "organization_id": self.organization_id,  # 组织ID（用于筛选）
            "deleted": self.deleted,
            "updated_at": fmt(self.updated_at)
        }

    def to_token_dict(self):
        return {
            "id": self.id,
            "name": self.username,  # 修改为username
            "role": self.role
        }


class Project(Base):
    __tablename__ = "projects"

    # 重写id字段，使用P前缀+时间戳+随机数生成，确保时间戳精确到毫秒
    id = Column(String(50), primary_key=True, default=lambda: f"P{int(dt.now().timestamp() * 1000)}{random.randint(1000, 9999)}")

    name = Column(String, index=True)
    description = Column(String)
    start_date = Column(Date)
    end_date = Column(Date)
    target = Column(Integer)
    owner_id = Column(String(50), ForeignKey("users.id"))
    organization_id = Column(String(50), ForeignKey("organizations.id"), nullable=True)  # 关联组织
    progress = Column(Float, default=0.0)

    # 关系映射
    owner = relationship("User", back_populates="projects")
    organization = relationship("Organization", back_populates="projects")
    tasks = relationship("Task", back_populates="project")

    def to_dict(self):
        def fmt(dt):
            if dt is None:
                return None
            return dt.strftime('%Y-%d-%m %H:%M:%S') if hasattr(dt, 'strftime') else str(dt)
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "start_date": fmt(self.start_date),
            "end_date": fmt(self.end_date),
            "target": self.target,
            "owner_id": self.owner_id,
            "organization_id": self.organization_id,
            "progress": self.progress
        }


class Task(Base):
    __tablename__ = "tasks"

    # 重写id字段，使用T前缀+时间戳+随机数生成，确保时间戳精确到毫秒
    id = Column(String(50), primary_key=True, default=lambda: f"T{int(dt.now().timestamp() * 1000)}{random.randint(1000, 9999)}")
    
    project_id = Column(String(50), ForeignKey("projects.id"))  # 修改为String类型
    name = Column(String, index=True)
    priority = Column(String, index=True)
    progress = Column(Float, default=0.0)
    start_date = Column(Date)
    end_date = Column(Date)
    owner_id = Column(String(50), ForeignKey("users.id"))
    related_task = Column(String(50), ForeignKey("tasks.id"), nullable=True)
    project = relationship("Project", back_populates="tasks")
    owner = relationship("User", back_populates="tasks")

    def to_dict(self):
        def fmt(dt):
            if dt is None:
                return None
            return dt.strftime('%Y-%d-%m %H:%M:%S') if hasattr(dt, 'strftime') else str(dt)
        return {
            "id": self.id,
            "project_id": self.project_id,
            "name": self.name,
            "priority": self.priority,
            "progress": self.progress,
            "start_date": fmt(self.start_date),
            "end_date": fmt(self.end_date),
            "owner_id": self.owner_id,
            "related_task": self.related_task
        }


class Merchant(Base):
    __tablename__ = "merchants"
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    password = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }
