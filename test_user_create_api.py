#!/usr/bin/env python3
"""
测试用户创建API接口
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_user_create_with_your_format():
    """测试您提供的参数格式"""
    print("=== 测试用户创建接口（您的参数格式） ===")
    
    # 使用您提供的参数格式
    payload = {
        "username": "user003",
        "email": "<EMAIL>",
        "name": "ya<PERSON> zhu",
        "role": "member",
        "department": "测试部门",
        "phone": "15519057432",
        "password": "123123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 用户创建成功")
                user_data = result.get("data", {})
                print(f"  用户ID: {user_data.get('id')}")
                print(f"  姓名: {user_data.get('name')}")
                print(f"  用户名: {user_data.get('username')}")
                print(f"  邮箱: {user_data.get('email')}")
                print(f"  角色: {user_data.get('role')}")
                print(f"  部门: {user_data.get('department')}")
                print(f"  电话: {user_data.get('phone')}")
                print(f"  状态: {user_data.get('status')}")
                
                # 验证敏感信息脱敏
                print("\n=== 验证敏感信息脱敏 ===")
                if "password" in user_data:
                    print("❌ 响应中包含密码字段！")
                else:
                    print("✅ 响应中不包含密码字段")
                
                email = user_data.get("email")
                phone = user_data.get("phone")
                if "*" in str(email):
                    print(f"✅ 邮箱已脱敏: {email}")
                else:
                    print(f"⚠️  邮箱未脱敏: {email}")
                
                if "*" in str(phone):
                    print(f"✅ 电话已脱敏: {phone}")
                else:
                    print(f"⚠️  电话未脱敏: {phone}")
                
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_user_create_with_new_department():
    """测试创建用户时自动创建新部门"""
    print("\n=== 测试自动创建新部门 ===")
    
    payload = {
        "username": "user004",
        "email": "<EMAIL>",
        "name": "新部门测试",
        "role": "member",
        "department": "新创建的部门",
        "phone": "13888888888",
        "password": "test123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户创建成功（新部门）")
                user_data = result.get("data", {})
                print(f"  用户名: {user_data.get('username')}")
                print(f"  部门: {user_data.get('department')}")
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_user_create_without_department():
    """测试创建用户时不指定部门"""
    print("\n=== 测试不指定部门 ===")
    
    payload = {
        "username": "user005",
        "email": "<EMAIL>",
        "name": "无部门用户",
        "role": "member",
        "phone": "13777777777",
        "password": "test123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户创建成功（无部门）")
                user_data = result.get("data", {})
                print(f"  用户名: {user_data.get('username')}")
                print(f"  部门: {user_data.get('department')}")
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_duplicate_username():
    """测试重复用户名"""
    print("\n=== 测试重复用户名 ===")
    
    payload = {
        "username": "user003",  # 重复的用户名
        "email": "<EMAIL>",
        "name": "重复用户名测试",
        "role": "member",
        "department": "测试部门",
        "phone": "13666666666",
        "password": "test123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") != 200:
                print("✅ 正确处理了重复用户名")
                print(f"  错误信息: {result.get('msg')}")
            else:
                print("❌ 未正确处理重复用户名")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_missing_required_fields():
    """测试缺少必填字段"""
    print("\n=== 测试缺少必填字段 ===")
    
    # 缺少用户名
    payload = {
        "email": "<EMAIL>",
        "name": "缺少用户名",
        "role": "member",
        "password": "test123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 422:  # FastAPI validation error
            print("✅ 正确处理了缺少必填字段")
        else:
            result = response.json()
            print(f"响应: {result}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def verify_created_users():
    """验证创建的用户"""
    print("\n=== 验证创建的用户 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                print("当前用户列表:")
                for user in users:
                    print(f"  - {user['name']} ({user['username']}) - {user['role']} - {user['department']} - {user['status']}")
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """运行所有测试"""
    print("开始测试用户创建API...")
    
    # 测试您提供的参数格式
    user_id1 = test_user_create_with_your_format()
    
    # 测试自动创建新部门
    user_id2 = test_user_create_with_new_department()
    
    # 测试不指定部门
    user_id3 = test_user_create_without_department()
    
    # 测试错误处理
    test_duplicate_username()
    test_missing_required_fields()
    
    # 验证创建的用户
    verify_created_users()
    
    print("\n=== 用户创建API测试完成 ===")
    print(f"创建的用户ID: {[uid for uid in [user_id1, user_id2, user_id3] if uid]}")

if __name__ == "__main__":
    main()
