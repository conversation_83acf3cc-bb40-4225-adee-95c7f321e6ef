2025-07-14 18:57:01 | error | ERROR | logging_middleware.py:180 | Request error: {"request_id": "REQ17524906201229673", "method": "POST", "path": "/users/create", "error_type": "PydanticSerializationError", "error_message": "Unable to serialize unknown type: <class 'models.User'>", "process_time_ms": 750.99, "traceback": "Traceback (most recent call last):\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 148, in call_next\n    message = await recv_stream.receive()\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\anyio\\streams\\memory.py\", line 126, in receive\n    raise EndOfStream from None\nanyio.EndOfStream\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\middleware\\logging_middleware.py\", line 44, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n                                   ~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py\", line 162, in __exit__\n    self.gen.throw(value)\n    ~~~~~~~~~~~~~~^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\middleware\\logging_middleware.py\", line 226, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\exceptions.py\", line 62, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 714, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 734, in app\n    await route.handle(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 288, in handle\n    await self.app(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 76, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 73, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\fastapi\\routing.py\", line 327, in app\n    content = await serialize_response(\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<9 lines>...\n    )\n    ^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\fastapi\\routing.py\", line 181, in serialize_response\n    return field.serialize(\n           ~~~~~~~~~~~~~~~^\n        value,\n        ^^^^^^\n    ...<5 lines>...\n        exclude_none=exclude_none,\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\fastapi\\_compat.py\", line 152, in serialize\n    return self._type_adapter.dump_python(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        value,\n        ^^^^^^\n    ...<6 lines>...\n        exclude_none=exclude_none,\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\pydantic\\type_adapter.py\", line 572, in dump_python\n    return self.serializer.to_python(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        instance,\n        ^^^^^^^^^\n    ...<11 lines>...\n        context=context,\n        ^^^^^^^^^^^^^^^^\n    )\n    ^\npydantic_core._pydantic_core.PydanticSerializationError: Unable to serialize unknown type: <class 'models.User'>\n"}
