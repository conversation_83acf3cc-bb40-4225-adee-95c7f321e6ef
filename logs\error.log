2025-07-14 18:57:01 | error | ERROR | logging_middleware.py:180 | Request error: {"request_id": "REQ17524906201229673", "method": "POST", "path": "/users/create", "error_type": "PydanticSerializationError", "error_message": "Unable to serialize unknown type: <class 'models.User'>", "process_time_ms": 750.99, "traceback": "Traceback (most recent call last):\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 148, in call_next\n    message = await recv_stream.receive()\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\anyio\\streams\\memory.py\", line 126, in receive\n    raise EndOfStream from None\nanyio.EndOfStream\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\middleware\\logging_middleware.py\", line 44, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 176, in __call__\n    with recv_stream, send_stream, collapse_excgroups():\n                                   ~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py\", line 162, in __exit__\n    self.gen.throw(value)\n    ~~~~~~~~~~~~~~^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_utils.py\", line 82, in collapse_excgroups\n    raise exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\middleware\\logging_middleware.py\", line 226, in dispatch\n    response = await call_next(request)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 156, in call_next\n    raise app_exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 141, in coro\n    await self.app(scope, receive_or_disconnect, send_no_error)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\middleware\\exceptions.py\", line 62, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 714, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 734, in app\n    await route.handle(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 288, in handle\n    await self.app(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 76, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 53, in wrapped_app\n    raise exc\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\_exception_handler.py\", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\starlette\\routing.py\", line 73, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\fastapi\\routing.py\", line 327, in app\n    content = await serialize_response(\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<9 lines>...\n    )\n    ^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\fastapi\\routing.py\", line 181, in serialize_response\n    return field.serialize(\n           ~~~~~~~~~~~~~~~^\n        value,\n        ^^^^^^\n    ...<5 lines>...\n        exclude_none=exclude_none,\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\fastapi\\_compat.py\", line 152, in serialize\n    return self._type_adapter.dump_python(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        value,\n        ^^^^^^\n    ...<6 lines>...\n        exclude_none=exclude_none,\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\pydantic\\type_adapter.py\", line 572, in dump_python\n    return self.serializer.to_python(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        instance,\n        ^^^^^^^^^\n    ...<11 lines>...\n        context=context,\n        ^^^^^^^^^^^^^^^^\n    )\n    ^\npydantic_core._pydantic_core.PydanticSerializationError: Unable to serialize unknown type: <class 'models.User'>\n"}
2025-07-14 19:03:38 | error | ERROR | logger_config.py:222 | Error occurred: {'error_type': 'IntegrityError', 'error_message': "(sqlite3.IntegrityError) UNIQUE constraint failed: users.email\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('17524910186956495', 'user003', 'yao zhu', '<EMAIL>', '15519057432', 'member', '$2b$12$Zk2ttO3FRQs49TJrTqMip.JX99m5Z/AlBQ8ZkZ0gIXVHUlZbwCfmi', '启用', 'O17524878576473295', '2025-07-14 19:03:38.695782', '2025-07-14 19:03:38.695783', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", 'context': {'operation': 'create_user', 'username': 'user003'}, 'user_id': None, 'request_id': None, 'traceback': 'Traceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlite3.IntegrityError: UNIQUE constraint failed: users.email\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\crud.py", line 310, in create_user\n    db.commit()\n    ~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2032, in commit\n    trans.commit(_to_root=True)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File "<string>", line 2, in commit\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1313, in commit\n    self._prepare_impl()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "<string>", line 2, in _prepare_impl\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1288, in _prepare_impl\n    self.session.flush()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4345, in flush\n    self._flush(objects)\n    ~~~~~~~~~~~^^^^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4480, in _flush\n    with util.safe_reraise():\n         ~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 224, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4441, in _flush\n    flush_context.execute()\n    ~~~~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 466, in execute\n    rec.execute(self)\n    ~~~~~~~~~~~^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 642, in execute\n    util.preloaded.orm_persistence.save_obj(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self.mapper,\n        ^^^^^^^^^^^^\n        uow.states_for_mapper_hierarchy(self.mapper, False, False),\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        uow,\n        ^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 93, in save_obj\n    _emit_insert_statements(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        base_mapper,\n        ^^^^^^^^^^^^\n    ...<3 lines>...\n        insert,\n        ^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 1233, in _emit_insert_statements\n    result = connection.execute(\n        statement,\n        params,\n        execution_options=execution_options,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1415, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 523, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1637, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1842, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1982, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2351, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (\'17524910186956495\', \'user003\', \'yao zhu\', \'<EMAIL>\', \'15519057432\', \'member\', \'$2b$12$Zk2ttO3FRQs49TJrTqMip.JX99m5Z/AlBQ8ZkZ0gIXVHUlZbwCfmi\', \'启用\', \'O17524878576473295\', \'2025-07-14 19:03:38.695782\', \'2025-07-14 19:03:38.695783\', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\n'}
2025-07-14 19:03:51 | error | ERROR | logger_config.py:222 | Error occurred: {'error_type': 'IntegrityError', 'error_message': "(sqlite3.IntegrityError) UNIQUE constraint failed: users.username\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('17524910317017175', 'user003', '重复用户名测试', '<EMAIL>', '13666666666', 'member', '$2b$12$IzUwRgCaQHQWrMdNoij.k.yaVWfJr69oy301TwBnu/.2XakrC3YXi', '启用', 'O17524878576473295', '2025-07-14 19:03:51.701061', '2025-07-14 19:03:51.701062', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", 'context': {'operation': 'create_user', 'username': 'user003'}, 'user_id': None, 'request_id': None, 'traceback': 'Traceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlite3.IntegrityError: UNIQUE constraint failed: users.username\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\crud.py", line 310, in create_user\n    db.commit()\n    ~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2032, in commit\n    trans.commit(_to_root=True)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File "<string>", line 2, in commit\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1313, in commit\n    self._prepare_impl()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "<string>", line 2, in _prepare_impl\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1288, in _prepare_impl\n    self.session.flush()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4345, in flush\n    self._flush(objects)\n    ~~~~~~~~~~~^^^^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4480, in _flush\n    with util.safe_reraise():\n         ~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 224, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4441, in _flush\n    flush_context.execute()\n    ~~~~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 466, in execute\n    rec.execute(self)\n    ~~~~~~~~~~~^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 642, in execute\n    util.preloaded.orm_persistence.save_obj(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self.mapper,\n        ^^^^^^^^^^^^\n        uow.states_for_mapper_hierarchy(self.mapper, False, False),\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        uow,\n        ^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 93, in save_obj\n    _emit_insert_statements(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        base_mapper,\n        ^^^^^^^^^^^^\n    ...<3 lines>...\n        insert,\n        ^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 1233, in _emit_insert_statements\n    result = connection.execute(\n        statement,\n        params,\n        execution_options=execution_options,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1415, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 523, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1637, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1842, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1982, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2351, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) UNIQUE constraint failed: users.username\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (\'17524910317017175\', \'user003\', \'重复用户名测试\', \'<EMAIL>\', \'13666666666\', \'member\', \'$2b$12$IzUwRgCaQHQWrMdNoij.k.yaVWfJr69oy301TwBnu/.2XakrC3YXi\', \'启用\', \'O17524878576473295\', \'2025-07-14 19:03:51.701061\', \'2025-07-14 19:03:51.701062\', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\n'}
2025-07-14 19:05:14 | error | ERROR | logger_config.py:222 | Error occurred: {'error_type': 'IntegrityError', 'error_message': "(sqlite3.IntegrityError) UNIQUE constraint failed: users.email\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('17524911142239541', 'test_duplicate_email', '重复邮箱测试', '<EMAIL>', '15519057432', 'member', '$2b$12$yNuGypvg4a1Flyd31KbP3uGLL8/1ZkSTVG4ZkWhqrxlmYjhmri4qW', '启用', 'O17524878576473295', '2025-07-14 19:05:14.223408', '2025-07-14 19:05:14.223409', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", 'context': {'operation': 'create_user', 'username': 'test_duplicate_email'}, 'user_id': None, 'request_id': None, 'traceback': 'Traceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlite3.IntegrityError: UNIQUE constraint failed: users.email\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\crud.py", line 310, in create_user\n    db.commit()\n    ~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2032, in commit\n    trans.commit(_to_root=True)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File "<string>", line 2, in commit\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1313, in commit\n    self._prepare_impl()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "<string>", line 2, in _prepare_impl\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1288, in _prepare_impl\n    self.session.flush()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4345, in flush\n    self._flush(objects)\n    ~~~~~~~~~~~^^^^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4480, in _flush\n    with util.safe_reraise():\n         ~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 224, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4441, in _flush\n    flush_context.execute()\n    ~~~~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 466, in execute\n    rec.execute(self)\n    ~~~~~~~~~~~^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 642, in execute\n    util.preloaded.orm_persistence.save_obj(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self.mapper,\n        ^^^^^^^^^^^^\n        uow.states_for_mapper_hierarchy(self.mapper, False, False),\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        uow,\n        ^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 93, in save_obj\n    _emit_insert_statements(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        base_mapper,\n        ^^^^^^^^^^^^\n    ...<3 lines>...\n        insert,\n        ^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 1233, in _emit_insert_statements\n    result = connection.execute(\n        statement,\n        params,\n        execution_options=execution_options,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1415, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 523, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1637, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1842, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1982, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2351, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (\'17524911142239541\', \'test_duplicate_email\', \'重复邮箱测试\', \'<EMAIL>\', \'15519057432\', \'member\', \'$2b$12$yNuGypvg4a1Flyd31KbP3uGLL8/1ZkSTVG4ZkWhqrxlmYjhmri4qW\', \'启用\', \'O17524878576473295\', \'2025-07-14 19:05:14.223408\', \'2025-07-14 19:05:14.223409\', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\n'}
2025-07-14 19:19:08 | error | ERROR | logger_config.py:222 | Error occurred: {'error_type': 'IntegrityError', 'error_message': "(sqlite3.IntegrityError) UNIQUE constraint failed: users.email\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: ('17524919482276923', 'user003', 'yao zhu', '<EMAIL>', '15519057432', 'member', '$2b$12$d0T/tYx1OTAZ9k/CpktmWe/6lnDTaWByv9ztrgOBbjIKDMn2NqKGC', '启用', 'O17524878576473295', '2025-07-14 19:19:08.228007', '2025-07-14 19:19:08.228008', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", 'context': {'operation': 'create_user', 'username': 'user003'}, 'user_id': None, 'request_id': None, 'traceback': 'Traceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlite3.IntegrityError: UNIQUE constraint failed: users.email\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\crud.py", line 310, in create_user\n    db.commit()\n    ~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2032, in commit\n    trans.commit(_to_root=True)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File "<string>", line 2, in commit\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1313, in commit\n    self._prepare_impl()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "<string>", line 2, in _prepare_impl\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py", line 139, in _go\n    ret_value = fn(self, *arg, **kw)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 1288, in _prepare_impl\n    self.session.flush()\n    ~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4345, in flush\n    self._flush(objects)\n    ~~~~~~~~~~~^^^^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4480, in _flush\n    with util.safe_reraise():\n         ~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 224, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 4441, in _flush\n    flush_context.execute()\n    ~~~~~~~~~~~~~~~~~~~~~^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 466, in execute\n    rec.execute(self)\n    ~~~~~~~~~~~^^^^^^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py", line 642, in execute\n    util.preloaded.orm_persistence.save_obj(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self.mapper,\n        ^^^^^^^^^^^^\n        uow.states_for_mapper_hierarchy(self.mapper, False, False),\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        uow,\n        ^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 93, in save_obj\n    _emit_insert_statements(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        base_mapper,\n        ^^^^^^^^^^^^\n    ...<3 lines>...\n        insert,\n        ^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py", line 1233, in _emit_insert_statements\n    result = connection.execute(\n        statement,\n        params,\n        execution_options=execution_options,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1415, in execute\n    return meth(\n        self,\n        distilled_parameters,\n        execution_options or NO_OPTIONS,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 523, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        self, distilled_params, execution_options\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1637, in _execute_clauseelement\n    ret = self._execute_context(\n        dialect,\n    ...<8 lines>...\n        cache_hit=cache_hit,\n    )\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1842, in _execute_context\n    return self._exec_single_context(\n           ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        dialect, context, statement, parameters\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1982, in _exec_single_context\n    self._handle_dbapi_exception(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        e, str_statement, effective_parameters, cursor, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2351, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1963, in _exec_single_context\n    self.dialect.do_execute(\n    ~~~~~~~~~~~~~~~~~~~~~~~^\n        cursor, str_statement, effective_parameters, context\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 943, in do_execute\n    cursor.execute(statement, parameters)\n    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^\nsqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) UNIQUE constraint failed: users.email\n[SQL: INSERT INTO users (id, username, name, email, phone, role, password, status, organization_id, created_at, updated_at, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]\n[parameters: (\'17524919482276923\', \'user003\', \'yao zhu\', \'<EMAIL>\', \'15519057432\', \'member\', \'$2b$12$d0T/tYx1OTAZ9k/CpktmWe/6lnDTaWByv9ztrgOBbjIKDMn2NqKGC\', \'启用\', \'O17524878576473295\', \'2025-07-14 19:19:08.228007\', \'2025-07-14 19:19:08.228008\', 0)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)\n'}
2025-07-14 19:27:16 | error | ERROR | logger_config.py:222 | Error occurred: {'error_type': 'ValueError', 'error_message': '用户名已存在', 'context': {'operation': 'create_user', 'username': 'user004'}, 'user_id': None, 'request_id': None, 'traceback': 'Traceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\crud.py", line 279, in create_user\n    raise ValueError("用户名已存在")\nValueError: 用户名已存在\n'}
2025-07-14 19:29:18 | error | ERROR | logger_config.py:222 | Error occurred: {'error_type': 'ValueError', 'error_message': '邮箱已存在', 'context': {'operation': 'create_user', 'username': 'user_dup_test2_1355'}, 'user_id': None, 'request_id': None, 'traceback': 'Traceback (most recent call last):\n  File "D:\\车秘系统相关文档\\projects\\project_manage\\backend\\crud.py", line 288, in create_user\n    raise ValueError("邮箱已存在")\nValueError: 邮箱已存在\n'}
