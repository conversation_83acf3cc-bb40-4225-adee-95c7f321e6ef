#!/usr/bin/env python3
"""
重新创建数据库表
"""

import os
from database import engine
from models import Base

def recreate_database():
    """重新创建数据库"""
    try:
        # 删除现有数据库文件
        db_files = ['project_management.db', 'project_management.db-journal']
        for db_file in db_files:
            if os.path.exists(db_file):
                os.remove(db_file)
                print(f"删除文件: {db_file}")
        
        # 创建所有表
        print("开始创建数据库表...")
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建完成")
        
        # 验证表创建
        import sqlite3
        conn = sqlite3.connect('project_management.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"创建的表: {[table[0] for table in tables]}")
        
        # 检查用户表结构
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        print(f"\n用户表字段:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 重新创建数据库失败: {e}")
        return False

if __name__ == "__main__":
    recreate_database()
