from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

import crud
from database import get_db
from schemas import DetailResponse, ListResponse, OrganizationCreate, OrganizationUpdate, Organization

router = APIRouter(
    prefix="/organizations",
    tags=["organizations"]
)

# 创建组织
@router.post("/add", response_model=DetailResponse)
def create_organization_endpoint(organization: OrganizationCreate, db: Session = Depends(get_db)):
    """创建新组织"""
    try:
        db_organization = crud.create_organization(db, organization)
        return DetailResponse(
            code=201, 
            msg="组织创建成功", 
            data=db_organization.to_dict()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建组织失败: {str(e)}")

# 获取组织列表
@router.get("/list", response_model=ListResponse)
def get_organizations_endpoint(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取组织列表"""
    try:
        organizations = crud.get_organizations(db, skip=skip, limit=limit)
        return ListResponse(
            code=200, 
            msg="获取组织列表成功", 
            data=[org.to_dict() for org in organizations]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取组织列表失败: {str(e)}")

# 获取单个组织详情
@router.get("/{organization_id}", response_model=DetailResponse)
def get_organization_endpoint(organization_id: str, db: Session = Depends(get_db)):
    """获取组织详情"""
    try:
        db_organization = crud.get_organization(db, organization_id)
        if not db_organization:
            raise HTTPException(status_code=404, detail="组织不存在")
        return DetailResponse(
            code=200, 
            msg="获取组织详情成功", 
            data=db_organization.to_dict()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取组织详情失败: {str(e)}")

# 更新组织信息
@router.put("/{organization_id}", response_model=DetailResponse)
def update_organization_endpoint(
    organization_id: str, 
    organization: OrganizationUpdate, 
    db: Session = Depends(get_db)
):
    """更新组织信息"""
    try:
        db_organization = crud.update_organization(db, organization_id, organization)
        if not db_organization:
            raise HTTPException(status_code=404, detail="组织不存在")
        return DetailResponse(
            code=200, 
            msg="组织更新成功", 
            data=db_organization.to_dict()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新组织失败: {str(e)}")

# 删除组织
@router.delete("/{organization_id}", response_model=DetailResponse)
def delete_organization_endpoint(organization_id: str, db: Session = Depends(get_db)):
    """删除组织（软删除）"""
    try:
        success = crud.delete_organization(db, organization_id)
        if not success:
            raise HTTPException(status_code=404, detail="组织不存在")
        return DetailResponse(
            code=200, 
            msg="组织删除成功", 
            data={"organization_id": organization_id, "deleted": True}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除组织失败: {str(e)}")

# 获取组织下的用户列表
@router.get("/{organization_id}/users", response_model=ListResponse)
def get_organization_users_endpoint(
    organization_id: str, 
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    """获取组织下的用户列表"""
    try:
        # 首先检查组织是否存在
        db_organization = crud.get_organization(db, organization_id)
        if not db_organization:
            raise HTTPException(status_code=404, detail="组织不存在")
        
        # 获取组织下的用户
        users = crud.get_users_by_organization(db, organization_id, skip=skip, limit=limit)
        return ListResponse(
            code=200, 
            msg="获取组织用户列表成功", 
            data=[user.to_dict() for user in users]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取组织用户列表失败: {str(e)}")

# 获取组织下的项目列表
@router.get("/{organization_id}/projects", response_model=ListResponse)
def get_organization_projects_endpoint(
    organization_id: str, 
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    """获取组织下的项目列表"""
    try:
        # 首先检查组织是否存在
        db_organization = crud.get_organization(db, organization_id)
        if not db_organization:
            raise HTTPException(status_code=404, detail="组织不存在")
        
        # 获取组织下的项目
        projects = crud.get_projects_by_organization(db, organization_id, skip=skip, limit=limit)
        return ListResponse(
            code=200, 
            msg="获取组织项目列表成功", 
            data=[project.to_dict() for project in projects]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取组织项目列表失败: {str(e)}")
