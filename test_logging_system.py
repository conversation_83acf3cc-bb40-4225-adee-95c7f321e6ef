#!/usr/bin/env python3
"""
测试日志系统
验证日志配置和记录功能是否正常工作
"""

import sys
import os
import time
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import (
    app_logger, api_logger, db_logger, auth_logger, 
    error_logger, performance_logger,
    log_function_call, log_database_operation, 
    log_auth_event, log_error, log_performance
)


def test_basic_logging():
    """测试基本日志功能"""
    print("=== 测试基本日志功能 ===")
    
    # 测试各种级别的日志
    app_logger.debug("这是一条调试信息")
    app_logger.info("这是一条信息日志")
    app_logger.warning("这是一条警告日志")
    app_logger.error("这是一条错误日志")
    app_logger.critical("这是一条严重错误日志")
    
    print("✅ 基本日志测试完成")


def test_specialized_loggers():
    """测试专用日志记录器"""
    print("\n=== 测试专用日志记录器 ===")
    
    # API日志
    api_logger.info("API请求开始")
    api_logger.info("API请求完成")
    
    # 数据库日志
    db_logger.info("数据库连接建立")
    db_logger.info("执行SQL查询")
    
    # 认证日志
    auth_logger.info("用户登录尝试")
    auth_logger.info("用户认证成功")
    
    # 错误日志
    error_logger.error("模拟错误情况")
    
    # 性能日志
    performance_logger.info("性能监控数据")
    
    print("✅ 专用日志记录器测试完成")


def test_helper_functions():
    """测试日志辅助函数"""
    print("\n=== 测试日志辅助函数 ===")
    
    # 测试函数调用日志
    log_function_call(
        func_name="test_function",
        args={"param1": "value1", "param2": "value2"},
        user_id="test_user_123",
        request_id="REQ123456789"
    )
    
    # 测试数据库操作日志
    log_database_operation(
        operation="CREATE",
        table="users",
        record_id="U123456789",
        user_id="admin"
    )
    
    # 测试认证事件日志
    log_auth_event(
        event="login",
        user_id="test_user",
        ip_address="*************",
        success=True
    )
    
    # 测试错误日志
    try:
        raise ValueError("这是一个测试错误")
    except Exception as e:
        log_error(
            error=e,
            context={"function": "test_helper_functions", "step": "error_simulation"},
            user_id="test_user",
            request_id="REQ123456789"
        )
    
    # 测试性能日志
    start_time = time.time()
    time.sleep(0.1)  # 模拟操作
    duration = time.time() - start_time
    
    log_performance(
        operation="test_operation",
        duration=duration,
        details={"records_processed": 100, "cache_hits": 85}
    )
    
    print("✅ 日志辅助函数测试完成")


def test_log_files():
    """测试日志文件是否正确创建"""
    print("\n=== 测试日志文件创建 ===")
    
    log_dir = Path("logs")
    expected_files = [
        "app.log",
        "api.log", 
        "database.log",
        "auth.log",
        "error.log",
        "performance.log"
    ]
    
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return False
    
    missing_files = []
    for file_name in expected_files:
        file_path = log_dir / file_name
        if file_path.exists():
            file_size = file_path.stat().st_size
            print(f"✅ {file_name} - {file_size} bytes")
        else:
            missing_files.append(file_name)
            print(f"❌ {file_name} - 文件不存在")
    
    if missing_files:
        print(f"缺少日志文件: {missing_files}")
        return False
    
    print("✅ 所有日志文件创建成功")
    return True


def test_api_logging():
    """测试API日志记录（需要服务器运行）"""
    print("\n=== 测试API日志记录 ===")
    
    base_url = "http://localhost:8000"
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{base_url}/docs", timeout=2)
        if response.status_code != 200:
            print("❌ 服务器未运行，跳过API日志测试")
            return
    except requests.exceptions.RequestException:
        print("❌ 无法连接到服务器，跳过API日志测试")
        return
    
    print("✅ 服务器运行中，开始测试API日志")
    
    # 测试正常请求
    try:
        response = requests.get(f"{base_url}/users/list")
        print(f"GET /users/list - 状态码: {response.status_code}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试错误请求
    try:
        response = requests.get(f"{base_url}/users/nonexistent_user_id")
        print(f"GET /users/nonexistent_user_id - 状态码: {response.status_code}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试POST请求
    try:
        test_user_data = {
            "username": "test_log_user",
            "password": "test123456",
            "role": "user",
            "status": "启用"
        }
        response = requests.post(f"{base_url}/users/create", json=test_user_data)
        print(f"POST /users/create - 状态码: {response.status_code}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("✅ API日志测试完成")


def test_log_rotation():
    """测试日志轮转功能"""
    print("\n=== 测试日志轮转功能 ===")
    
    # 生成大量日志来测试轮转
    for i in range(1000):
        app_logger.info(f"日志轮转测试消息 {i+1}")
        if i % 100 == 0:
            print(f"已生成 {i+1} 条日志")
    
    print("✅ 日志轮转测试完成")


def generate_sample_logs():
    """生成示例日志数据"""
    print("\n=== 生成示例日志数据 ===")
    
    # 模拟各种场景的日志
    scenarios = [
        ("用户登录", lambda: log_auth_event("login", "user123", "*************", True)),
        ("用户登录失败", lambda: log_auth_event("login", "user456", "192.168.1.101", False)),
        ("创建任务", lambda: log_database_operation("CREATE", "tasks", "T123456789", "user123")),
        ("更新项目", lambda: log_database_operation("UPDATE", "projects", "P123456789", "user123")),
        ("API调用", lambda: api_logger.info("API调用: GET /tasks/list")),
        ("慢查询", lambda: log_performance("slow_query", 2.5, {"query": "SELECT * FROM tasks"})),
        ("系统错误", lambda: error_logger.error("数据库连接超时")),
    ]
    
    for scenario_name, log_func in scenarios:
        for i in range(5):
            log_func()
            time.sleep(0.01)  # 避免时间戳完全相同
        print(f"✅ 生成 {scenario_name} 日志")
    
    print("✅ 示例日志数据生成完成")


def main():
    """主测试函数"""
    print("开始测试日志系统...")
    print("=" * 50)
    
    # 基本功能测试
    test_basic_logging()
    test_specialized_loggers()
    test_helper_functions()
    
    # 文件系统测试
    if not test_log_files():
        print("❌ 日志文件测试失败")
        return
    
    # 生成示例数据
    generate_sample_logs()
    
    # API测试（可选）
    test_api_logging()
    
    # 轮转测试
    test_log_rotation()
    
    print("\n" + "=" * 50)
    print("✅ 日志系统测试完成")
    print("\n使用以下命令查看日志:")
    print("python log_viewer.py --summary")
    print("python log_viewer.py --tail app")
    print("python log_viewer.py --search 'ERROR'")
    print("python log_viewer.py --api-stats")


if __name__ == "__main__":
    main()
