#!/usr/bin/env python3
"""
测试邮箱和手机号非必填功能
"""

import requests
import json
import random

BASE_URL = "http://localhost:8000"

def test_create_user_without_email_phone():
    """测试创建用户时不提供邮箱和手机号"""
    print("=== 测试不提供邮箱和手机号 ===")
    
    random_num = random.randint(1000, 9999)
    
    payload = {
        "username": f"user_no_contact_{random_num}",
        "name": "无联系方式用户",
        "role": "member",
        "department": "测试部门",
        "password": "123123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 用户创建成功（无邮箱和手机号）")
                user_data = result.get("data", {})
                
                print(f"\n=== 创建的用户信息 ===")
                print(f"  用户ID: {user_data.get('id')}")
                print(f"  姓名: {user_data.get('name')}")
                print(f"  用户名: {user_data.get('username')}")
                print(f"  邮箱: {user_data.get('email')}")
                print(f"  电话: {user_data.get('phone')}")
                print(f"  角色: {user_data.get('role')}")
                print(f"  部门: {user_data.get('department')}")
                
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_create_user_with_only_email():
    """测试只提供邮箱，不提供手机号"""
    print("\n=== 测试只提供邮箱 ===")
    
    random_num = random.randint(1000, 9999)
    
    payload = {
        "username": f"user_email_only_{random_num}",
        "name": "只有邮箱用户",
        "email": f"email_only_{random_num}@example.com",
        "role": "member",
        "department": "测试部门",
        "password": "123123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户创建成功（只有邮箱）")
                user_data = result.get("data", {})
                print(f"  邮箱: {user_data.get('email')}")
                print(f"  电话: {user_data.get('phone')}")
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_create_user_with_only_phone():
    """测试只提供手机号，不提供邮箱"""
    print("\n=== 测试只提供手机号 ===")
    
    random_num = random.randint(1000, 9999)
    
    payload = {
        "username": f"user_phone_only_{random_num}",
        "name": "只有手机号用户",
        "phone": f"138{random_num:04d}8888",
        "role": "member",
        "department": "测试部门",
        "password": "123123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户创建成功（只有手机号）")
                user_data = result.get("data", {})
                print(f"  邮箱: {user_data.get('email')}")
                print(f"  电话: {user_data.get('phone')}")
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_create_user_with_empty_email_phone():
    """测试提供空字符串的邮箱和手机号"""
    print("\n=== 测试空字符串邮箱和手机号 ===")
    
    random_num = random.randint(1000, 9999)
    
    payload = {
        "username": f"user_empty_contact_{random_num}",
        "name": "空联系方式用户",
        "email": "",  # 空字符串
        "phone": "",  # 空字符串
        "role": "member",
        "department": "测试部门",
        "password": "123123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/users/create", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户创建成功（空字符串联系方式）")
                user_data = result.get("data", {})
                print(f"  邮箱: {user_data.get('email')}")
                print(f"  电话: {user_data.get('phone')}")
                return user_data.get("id")
            else:
                print(f"❌ 创建失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

def test_duplicate_email_handling():
    """测试重复邮箱处理（当邮箱不为空时）"""
    print("\n=== 测试重复邮箱处理 ===")
    
    # 先创建一个有邮箱的用户
    random_num = random.randint(1000, 9999)
    email = f"duplicate_test_{random_num}@example.com"
    
    payload1 = {
        "username": f"user_dup_test1_{random_num}",
        "name": "重复邮箱测试1",
        "email": email,
        "role": "member",
        "password": "123123"
    }
    
    try:
        response1 = requests.post(f"{BASE_URL}/users/create", json=payload1)
        if response1.status_code == 200 and response1.json().get("code") == 200:
            print("✅ 第一个用户创建成功")
            
            # 尝试创建第二个用户，使用相同邮箱
            payload2 = {
                "username": f"user_dup_test2_{random_num}",
                "name": "重复邮箱测试2",
                "email": email,  # 相同邮箱
                "role": "member",
                "password": "123123"
            }
            
            response2 = requests.post(f"{BASE_URL}/users/create", json=payload2)
            if response2.status_code == 200:
                result2 = response2.json()
                if result2.get("code") == 400:
                    print("✅ 正确处理了重复邮箱")
                    print(f"  错误信息: {result2.get('msg')}")
                else:
                    print(f"❌ 未正确处理重复邮箱: {result2}")
            else:
                print(f"❌ HTTP错误: {response2.status_code}")
        else:
            print("❌ 第一个用户创建失败")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_multiple_users_without_email():
    """测试创建多个没有邮箱的用户（应该都能成功）"""
    print("\n=== 测试多个无邮箱用户 ===")
    
    success_count = 0
    for i in range(3):
        random_num = random.randint(1000, 9999)
        payload = {
            "username": f"user_no_email_{i}_{random_num}",
            "name": f"无邮箱用户{i+1}",
            "role": "member",
            "password": "123123"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/users/create", json=payload)
            if response.status_code == 200 and response.json().get("code") == 200:
                success_count += 1
                print(f"✅ 用户{i+1}创建成功")
            else:
                print(f"❌ 用户{i+1}创建失败")
        except Exception as e:
            print(f"❌ 用户{i+1}创建异常: {e}")
    
    print(f"成功创建 {success_count}/3 个无邮箱用户")

def verify_user_list():
    """验证用户列表中的联系方式显示"""
    print("\n=== 验证用户列表中的联系方式 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                
                print("检查最新创建的用户:")
                for user in users[-5:]:  # 检查最后5个用户
                    email = user.get("email")
                    phone = user.get("phone")
                    
                    print(f"\n用户: {user.get('name')} ({user.get('username')})")
                    print(f"  邮箱: {email if email else '未提供'}")
                    print(f"  电话: {phone if phone else '未提供'}")
                    
                    # 验证空值处理
                    if email is None:
                        print(f"  ✅ 邮箱正确处理为None")
                    elif email == "":
                        print(f"  ⚠️  邮箱为空字符串")
                    
                    if phone is None:
                        print(f"  ✅ 电话正确处理为None")
                    elif phone == "":
                        print(f"  ⚠️  电话为空字符串")
                        
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("开始测试邮箱和手机号非必填功能...")
    
    # 测试各种情况
    test_create_user_without_email_phone()
    test_create_user_with_only_email()
    test_create_user_with_only_phone()
    test_create_user_with_empty_email_phone()
    
    # 测试重复邮箱处理
    test_duplicate_email_handling()
    
    # 测试多个无邮箱用户
    test_multiple_users_without_email()
    
    # 验证用户列表
    verify_user_list()
    
    print("\n=== 邮箱和手机号非必填功能测试完成 ===")

if __name__ == "__main__":
    main()
