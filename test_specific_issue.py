#!/usr/bin/env python3
"""
测试具体的分页问题：page=2&page_size=10
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_page_size_vs_pageSize():
    """测试page_size和pageSize参数的区别"""
    print("=== 测试page_size vs pageSize参数 ===")
    
    test_cases = [
        {"url": f"{BASE_URL}/users/list?page=1&pageSize=10", "desc": "使用pageSize参数"},
        {"url": f"{BASE_URL}/users/list?page=1&page_size=10", "desc": "使用page_size参数"},
        {"url": f"{BASE_URL}/users/list?page=2&pageSize=10", "desc": "第2页，使用pageSize参数"},
        {"url": f"{BASE_URL}/users/list?page=2&page_size=10", "desc": "第2页，使用page_size参数"},
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['desc']} ---")
        print(f"URL: {test_case['url']}")
        
        try:
            response = requests.get(test_case['url'])
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    data = result.get("data", {})
                    items = data.get("items", [])
                    pagination = data.get("pagination", {})
                    
                    print(f"返回用户数: {len(items)}")
                    print(f"分页信息: {pagination}")
                    
                    if items:
                        print(f"第一个用户: {items[0].get('username')}")
                        if len(items) > 1:
                            print(f"最后一个用户: {items[-1].get('username')}")
                else:
                    print(f"❌ API错误: {result.get('msg')}")
                    print(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def test_different_pages():
    """测试不同页码"""
    print(f"\n=== 测试不同页码 ===")
    
    page_size = 5  # 使用较小的页面大小以便看到分页效果
    
    for page in range(1, 5):  # 测试前4页
        print(f"\n--- 第{page}页 ---")
        
        try:
            # 使用pageSize参数
            response = requests.get(f"{BASE_URL}/users/list?page={page}&pageSize={page_size}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    data = result.get("data", {})
                    items = data.get("items", [])
                    pagination = data.get("pagination", {})
                    
                    print(f"返回用户数: {len(items)}")
                    print(f"总数: {pagination.get('total')}")
                    print(f"总页数: {pagination.get('totalPages')}")
                    print(f"是否有下一页: {pagination.get('hasNext')}")
                    
                    if items:
                        usernames = [user.get('username') for user in items]
                        print(f"用户列表: {usernames}")
                    else:
                        print("❌ 没有返回用户数据")
                        
                else:
                    print(f"❌ API错误: {result.get('msg')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n=== 测试边界情况 ===")
    
    test_cases = [
        {"page": 0, "pageSize": 10, "desc": "页码为0"},
        {"page": -1, "pageSize": 10, "desc": "页码为负数"},
        {"page": 1, "pageSize": 0, "desc": "页面大小为0"},
        {"page": 1, "pageSize": -1, "desc": "页面大小为负数"},
        {"page": 999, "pageSize": 10, "desc": "页码超出范围"},
        {"page": 1, "pageSize": 1000, "desc": "页面大小很大"},
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['desc']} ---")
        
        try:
            url = f"{BASE_URL}/users/list?page={test_case['page']}&pageSize={test_case['pageSize']}"
            response = requests.get(url)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    data = result.get("data", {})
                    items = data.get("items", [])
                    pagination = data.get("pagination", {})
                    
                    print(f"返回用户数: {len(items)}")
                    print(f"分页信息: {pagination}")
                else:
                    print(f"API错误: {result.get('msg')}")
            else:
                print(f"HTTP错误: {response.status_code}")
                print(f"响应: {response.text[:200]}...")
                
        except Exception as e:
            print(f"请求异常: {e}")

def main():
    """主函数"""
    print("开始测试具体的分页问题...")
    
    # 测试参数差异
    test_page_size_vs_pageSize()
    
    # 测试不同页码
    test_different_pages()
    
    # 测试边界情况
    test_edge_cases()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
