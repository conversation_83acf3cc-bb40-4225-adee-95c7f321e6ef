2025-07-14 16:11:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524807143423963", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:11:55 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524807143423963", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 772.09, "response_headers": {"content-length": "465", "content-type": "application/json"}}
2025-07-14 16:15:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524809101348963", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:15:10 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:15:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524809101348963", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 288.08, "response_headers": {"content-length": "465", "content-type": "application/json"}}
2025-07-14 16:15:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524809280176280", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:15:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524809280176280", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 3.36, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:15:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524809372746886", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:15:37 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:15:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524809372746886", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 264.7, "response_headers": {"content-length": "465", "content-type": "application/json"}}
2025-07-14 16:17:11 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524810316824121", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:17:11 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524810316824121", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.89, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:17:24 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524810441748092", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:17:24 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:17:24 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524810441748092", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 252.27, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:19:41 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524811813555193", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:19:41 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524811813555193", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.71, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:19:56 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524811961382487", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:19:56 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:19:56 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524811961382487", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 244.91, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:21:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524813177208782", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:21:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524813177208782", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 4.62, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:22:06 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524813263222755", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:22:06 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:22:06 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524813263222755", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 238.06, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:22:12 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524813322465803", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:22:12 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524813322465803", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 6.05, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:29:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524817624309434", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:29:22 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:29:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524817624309434", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 244.64, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:30:00 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524818007626377", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:30:00 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524818007626377", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.04, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:30:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524818229771607", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:30:23 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:30:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524818229771607", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 240.57, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:44:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524826660689644", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752482193207", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8080", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8080/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:44:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524826660689644", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.87, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:20:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524848283889342", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752483963008", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8080", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8080/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:20:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524848283889342", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 14.36, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:22:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849495265553", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:22:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849495265553", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.9, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:22:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849556124322", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:22:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849556124322", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 295.91, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:22:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849772534388", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:22:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849772534388", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.56, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:23:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849881571283", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:23:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849881571283", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 265.39, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:24:04 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850448149447", "method": "POST", "url": "http://127.0.0.1:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "43", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:24:05 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850448149447", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 293.41, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:24:05 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850451112036", "method": "POST", "url": "http://127.0.0.1:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "0", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlkIjoiMTc1MjQ3ODMwMjQyNTAwMDEiLCJleHAiOjE3NTI0ODY4NDV9.q6OWN7ZJfJKaN8sjb4cj85e7Oa5VqfKQHZpXu1EFkCA", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": "authenticated_user", "body": null}
2025-07-14 17:24:05 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850451112036", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 36.51, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850900232106", "method": "POST", "url": "http://127.0.0.1:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "43", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850900232106", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 243.8, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850902771432", "method": "POST", "url": "http://127.0.0.1:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "0", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlkIjoiMTc1MjQ3ODMwMjQyNTAwMDEiLCJleHAiOjE3NTI0ODY4OTB9.EBtpM7Oy08jMx5H1YHW-plaT18v42pXsT7kmd8GpEkY", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": "authenticated_user", "body": null}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850902771432", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.57, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524851296906819", "method": "POST", "url": "http://127.0.0.1:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "43", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524851296906819", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 266.28, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524851299679249", "method": "POST", "url": "http://127.0.0.1:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "0", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlkIjoiMTc1MjQ3ODMwMjQyNTAwMDEiLCJleHAiOjE3NTI0ODY5Mjl9.5FBx4TEhT9Ic23OsrKGdVUfOUCt3qDRy5Y8LcBv308A", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": "authenticated_user", "body": null}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524851299679249", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 6.9, "response_headers": {"content-length": "61", "content-type": "application/json"}}
2025-07-14 17:26:07 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524851677935217", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752484838075", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8080", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8080/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:26:07 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524851677935217", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.56, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:31:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524855008072187", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:31:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524855008072187", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.46, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:35:45 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524857451865277", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485515921", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:35:45 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524857451865277", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.98, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870026457620", "method": "GET", "url": "http://localhost:8000/users?page=1&pageSize=20", "path": "/users", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870026479840", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870026457620", "method": "GET", "path": "/users", "status_code": 404, "process_time_ms": 2.6, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870026479840", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 2.51, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:57:00 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870201467784", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:00 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870201467784", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 15.1, "response_headers": {"content-length": "526", "content-type": "application/json"}}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870228755900", "method": "GET", "url": "http://localhost:8000/users?page=1&pageSize=20", "path": "/users", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870228755900", "method": "GET", "path": "/users", "status_code": 404, "process_time_ms": 2.08, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870228797065", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870228797065", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 1.05, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:57:04 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870241699105", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:04 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870241699105", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.17, "response_headers": {"content-length": "525", "content-type": "application/json"}}
2025-07-14 17:57:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870281827649", "method": "GET", "url": "http://localhost:8000/users/list?search=test&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "test", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870281827649", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.32, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:57:12 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870321971854", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:12 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870321971854", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.7, "response_headers": {"content-length": "342", "content-type": "application/json"}}
2025-07-14 17:57:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870362161621", "method": "GET", "url": "http://localhost:8000/users/list?status=active&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "active", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870362161621", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.89, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:58:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870957047193", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870957047193", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.74, "response_headers": {"content-length": "540", "content-type": "application/json"}}
2025-07-14 17:58:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870997229561", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870997229561", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.22, "response_headers": {"content-length": "539", "content-type": "application/json"}}
2025-07-14 17:58:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871037353022", "method": "GET", "url": "http://localhost:8000/users/list?search=test&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "test", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871037353022", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.38, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:58:27 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871077528981", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:27 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871077528981", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.8, "response_headers": {"content-length": "349", "content-type": "application/json"}}
2025-07-14 17:58:31 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871117676828", "method": "GET", "url": "http://localhost:8000/users/list?status=active&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "active", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:31 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871117676828", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.44, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:59:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871623456018", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871623456018", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 8.54, "response_headers": {"content-length": "540", "content-type": "application/json"}}
2025-07-14 17:59:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871663691434", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871663691434", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.41, "response_headers": {"content-length": "539", "content-type": "application/json"}}
2025-07-14 17:59:30 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871703874081", "method": "GET", "url": "http://localhost:8000/users/list?search=test&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "test", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:30 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871703874081", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.87, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:59:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871744007684", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871744007684", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.03, "response_headers": {"content-length": "349", "content-type": "application/json"}}
2025-07-14 17:59:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871784151989", "method": "GET", "url": "http://localhost:8000/users/list?status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871784151989", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.14, "response_headers": {"content-length": "540", "content-type": "application/json"}}
2025-07-14 17:59:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871824275423", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871824275423", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.31, "response_headers": {"content-length": "349", "content-type": "application/json"}}
2025-07-14 18:11:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524878980019032", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524878980019032", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.54, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:11:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879020211781", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879020211781", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.51, "response_headers": {"content-length": "1673", "content-type": "application/json"}}
2025-07-14 18:11:46 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879060347928", "method": "GET", "url": "http://localhost:8000/users/list?search=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:46 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879060347928", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.34, "response_headers": {"content-length": "460", "content-type": "application/json"}}
2025-07-14 18:11:50 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879100487894", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:50 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879100487894", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.48, "response_headers": {"content-length": "460", "content-type": "application/json"}}
2025-07-14 18:11:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879140631992", "method": "GET", "url": "http://localhost:8000/users/list?status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879140631992", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.93, "response_headers": {"content-length": "469", "content-type": "application/json"}}
2025-07-14 18:11:58 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879180869499", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:58 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879180869499", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.45, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879584179385", "method": "GET", "url": "http://localhost:8000/users?page=1&pageSize=20", "path": "/users", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879584198758", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879584179385", "method": "GET", "path": "/users", "status_code": 404, "process_time_ms": 3.05, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879584198758", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 2.45, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:13:43 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880232046286", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:13:43 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880232046286", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 33.54, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:14:24 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880641946643", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&search=admin&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "search": "admin", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:24 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880641946643", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 20.5, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:14:48 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880886499733", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&search=0&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "search": "0", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:48 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880886499733", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.56, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:14:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880947039638", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&search=0&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "search": "0", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880947039638", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.84, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:14:59 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880994119083", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:59 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880994119083", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 8.8, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881403676476", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881403837377", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881403676476", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 25.84, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881403837377", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 68.82, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881404924293", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881405277097", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881404924293", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 233.68, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881405277097", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 228.32, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881407725694", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881407725694", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.36, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756165253", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756187619", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756187619", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 1.71, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756165253", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.03, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756841242", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756873848", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756841242", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 11.6, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756873848", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 18.93, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881757749051", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881757749051", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.17, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882881524623", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882881549301", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882881524623", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 73.22, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882881549301", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 72.22, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882882759072", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882882777939", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882882777939", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 8.94, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882882759072", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 31.3, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882883317651", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882883317651", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.23, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882995995731", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882995995731", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.55, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882996074107", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882996074107", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 7.67, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882996808448", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882996808448", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.84, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882997108863", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882997108863", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.67, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882997524827", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882997524827", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 13.8, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883092611088", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883092611088", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.2, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883093224276", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883093257404", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883093257404", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 16.27, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883093224276", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 37.88, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883093641187", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883093641187", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.12, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883175133333", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883175133333", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.28, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883175218962", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883175218962", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.24, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883176126143", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883176126143", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 11.07, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883176269949", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883176269949", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 13.97, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883176429683", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883176429683", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 12.24, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883375923983", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883375938214", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883375923983", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 51.24, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883375938214", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 60.75, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883376866978", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883376899015", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883376899015", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 17.66, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883376866978", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 41.88, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883377496071", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883377496071", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 53.5, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883507666571", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883507666571", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.01, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883507734349", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883507734349", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.76, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883508055229", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883508055229", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.12, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883508199321", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883508199321", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.63, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883508693304", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883508693304", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 14.48, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883662582605", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883662611224", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883662611224", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 93.53, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883662582605", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 100.75, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883663866609", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883663876442", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883663876442", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 20.47, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883663866609", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 31.11, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883664263598", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883664263598", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.99, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883801633795", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883801693279", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883801633795", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 66.95, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883801693279", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 65.61, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883803019734", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883803034176", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883803034176", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.31, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883803019734", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 19.61, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883803344286", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883803344286", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 12.51, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884209368327", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884209368327", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.53, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884209432461", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884209432461", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.35, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884210057503", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884210057503", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.97, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884210121150", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884210121150", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.72, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884210949990", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884210949990", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.76, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884825665259", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884825683966", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884825683966", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 7.31, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884825665259", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 16.71, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885138347124", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885138347124", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.15, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885138755182", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885138755182", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 12.94, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885331731402", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885331731402", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.92, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885331809634", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885331809634", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 5.23, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885438164348", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885438164348", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.61, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885438228623", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885438228623", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 9.74, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885577013023", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885577013023", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 8.38, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885577661261", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885577661261", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 63.13, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:22:43 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885637189429", "method": "PATCH", "url": "http://localhost:8000/users/17524878596987671/status", "path": "/users/17524878596987671/status", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "19", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"status": "active"}}
2025-07-14 18:22:43 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885637189429", "method": "PATCH", "path": "/users/17524878596987671/status", "status_code": 404, "process_time_ms": 1.41, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887206109777", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887206114687", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887206114687", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 18.28, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887206109777", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.52, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887288608265", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887288608265", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 61.45, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887289752099", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887289752099", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 11.42, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887389336767", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887389351448", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887389336767", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 53.46, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887389351448", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 58.46, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887488527253", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887488539157", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887488539157", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 16.43, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887488527253", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.74, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:26:03 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887635079820", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:26:03 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887635088650", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:26:03 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887635088650", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 18.79, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:26:03 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887635079820", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.32, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:26:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887884456988", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:26:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887884456988", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.01, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:26:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887884582239", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:26:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887884582239", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 10.42, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:26:45 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524888053801296", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:26:45 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524888053801296", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.85, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:26:45 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524888053857111", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:26:45 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524888053857111", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.67, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:28:30 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889108626069", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:28:30 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889108644693", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:28:30 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889108644693", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 5.58, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:28:30 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889108626069", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.35, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:29:01 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889415758177", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:29:01 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889415758177", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.35, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:29:01 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889415876421", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:29:01 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889415876421", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 5.51, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:29:13 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889537468168", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:29:13 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889537468168", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.85, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:29:13 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889537608651", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:29:13 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889537608651", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 12.53, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:29:24 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889642336275", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:29:24 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889642336275", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.43, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:29:24 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524889642543555", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:29:24 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524889642543555", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 43.28, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:30:43 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524890437472456", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:30:43 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524890437486382", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:30:43 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524890437486382", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 5.5, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:30:43 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524890437472456", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 8.08, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:32:05 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891251872845", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:05 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891251883298", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:05 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891251883298", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.41, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:32:05 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891251872845", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 8.99, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:32:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891353717304", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891353717304", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.46, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:32:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891353814351", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891353814351", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 3.31, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:32:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891494858539", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891494858539", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.19, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:32:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891494985713", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891494985713", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 10.33, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:32:46 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891665479816", "method": "POST", "url": "http://localhost:8000/users/deactivate", "path": "/users/deactivate", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "34", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"user_ids": ["17524878590921916"]}}
2025-07-14 18:32:46 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891665479816", "method": "POST", "path": "/users/deactivate", "status_code": 200, "process_time_ms": 83.4, "response_headers": {"content-length": "130", "content-type": "application/json"}}
2025-07-14 18:32:46 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891666405866", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:46 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891666405866", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.17, "response_headers": {"content-length": "1676", "content-type": "application/json"}}
2025-07-14 18:32:52 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891725499026", "method": "POST", "url": "http://localhost:8000/users/activate", "path": "/users/activate", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "34", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"user_ids": ["17524878590921916"]}}
2025-07-14 18:32:52 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891725499026", "method": "POST", "path": "/users/activate", "status_code": 200, "process_time_ms": 102.28, "response_headers": {"content-length": "128", "content-type": "application/json"}}
2025-07-14 18:32:52 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524891726606261", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:32:52 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524891726606261", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.35, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:34:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524892638302539", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:34:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524892638313791", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:34:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524892638313791", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 5.57, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:34:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524892638302539", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.53, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:34:39 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524892794694501", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:34:39 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524892794694501", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.3, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:34:39 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524892794795811", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:34:39 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524892794795811", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.53, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:35:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524893166475282", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:35:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524893166475282", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.35, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:35:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524893166785390", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:35:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524893166785390", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 56.33, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:35:17 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524893179076204", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:35:17 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524893179167124", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:35:17 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524893179076204", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 63.75, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:35:17 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524893179167124", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 68.75, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:35:51 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524893514035277", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:35:51 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524893514046917", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:35:51 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524893514046917", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.53, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:35:51 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524893514035277", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.44, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:36:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894001192902", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:36:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894001192902", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.58, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:36:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894001278919", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:36:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894001278919", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 7.03, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:36:56 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894162158707", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:36:56 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894162158707", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 31.55, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:36:56 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894162744442", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:36:56 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894162744442", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 10.98, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:37:11 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894315303996", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:11 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894315303996", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 19.42, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:37:11 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894315528442", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:11 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894315528442", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.95, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:37:25 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894453725296", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:25 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894453725296", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.34, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:37:25 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894453818553", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:25 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894453818553", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 6.84, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:37:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894492832859", "method": "POST", "url": "http://localhost:8000/users/activate", "path": "/users/activate", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "34", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"user_ids": ["17524878596987671"]}}
2025-07-14 18:37:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894492832859", "method": "POST", "path": "/users/activate", "status_code": 200, "process_time_ms": 634.12, "response_headers": {"content-length": "128", "content-type": "application/json"}}
2025-07-14 18:37:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894499312874", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894499312874", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.53, "response_headers": {"content-length": "1672", "content-type": "application/json"}}
2025-07-14 18:37:32 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894525217944", "method": "POST", "url": "http://localhost:8000/users/deactivate", "path": "/users/deactivate", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "34", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"user_ids": ["17524878596987671"]}}
2025-07-14 18:37:32 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894525217944", "method": "POST", "path": "/users/deactivate", "status_code": 200, "process_time_ms": 99.45, "response_headers": {"content-length": "130", "content-type": "application/json"}}
2025-07-14 18:37:32 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894526271788", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:32 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894526271788", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.69, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:37:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894604785426", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894604785426", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.98, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:37:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524894604854322", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:37:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524894604854322", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 8.95, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:39:47 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524895876115443", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:39:47 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524895876134115", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:39:47 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524895876115443", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.68, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:39:47 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524895876134115", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 11.23, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:40:01 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896015661739", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:01 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896015661739", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.0, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:40:01 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896015898020", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:01 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896015898020", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 17.4, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:40:14 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896144704480", "method": "PUT", "url": "http://localhost:8000/users/17524878590921916", "path": "/users/17524878590921916", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "165", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"username": "manager001", "email": "<EMAIL>", "name": "张经理", "role": "manager", "department": "测试部门", "phone": "13800138001", "password": "1234"}}
2025-07-14 18:40:14 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896144704480", "method": "PUT", "path": "/users/17524878590921916", "status_code": 405, "process_time_ms": 1.6, "response_headers": {"allow": "DELETE", "content-length": "31", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:40:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896163546715", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896163546715", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.97, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:40:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896163609574", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896163609574", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 6.05, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:40:30 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896302855200", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:30 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896302855200", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.8, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:40:30 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896302918627", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:30 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896302918627", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 3.93, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:40:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896420972497", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896420972497", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.58, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:40:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896421058896", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896421058896", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 5.73, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:40:56 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896563116703", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:56 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896563116703", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.92, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:40:56 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524896563194868", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:40:56 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524896563194868", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 6.5, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:45:53 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524899531149259", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:45:53 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524899531156064", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:45:53 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524899531149259", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 6.26, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:45:53 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524899531156064", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.96, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:46:02 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524899620384749", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:46:02 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524899620384749", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.97, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:46:02 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524899620454369", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:46:02 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524899620454369", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.19, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:46:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524899756891115", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:46:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524899756891115", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.28, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:46:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524899756973812", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:46:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524899756973812", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.35, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:46:50 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900101466901", "method": "PATCH", "url": "http://localhost:8000/users/17524878594012259/status", "path": "/users/17524878594012259/status", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "21", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"status": "inactive"}}
2025-07-14 18:46:50 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900101466901", "method": "PATCH", "path": "/users/17524878594012259/status", "status_code": 405, "process_time_ms": 1.91, "response_headers": {"allow": "PUT", "content-length": "31", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:47:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900462958302", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:47:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900463234003", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:47:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900462958302", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 138.18, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:47:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900463234003", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 170.87, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:47:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900548848450", "method": "PUT", "url": "http://localhost:8000/users/17524878594012259/status", "path": "/users/17524878594012259/status", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "21", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"status": "inactive"}}
2025-07-14 18:47:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900548848450", "method": "PUT", "path": "/users/17524878594012259/status", "status_code": 200, "process_time_ms": 113.5, "response_headers": {"content-length": "366", "content-type": "application/json"}}
2025-07-14 18:47:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900550071101", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:47:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900550071101", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 16.84, "response_headers": {"content-length": "1676", "content-type": "application/json"}}
2025-07-14 18:47:41 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900613654609", "method": "PUT", "url": "http://localhost:8000/users/17524878594012259/status", "path": "/users/17524878594012259/status", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "19", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"status": "active"}}
2025-07-14 18:47:41 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900613654609", "method": "PUT", "path": "/users/17524878594012259/status", "status_code": 200, "process_time_ms": 161.44, "response_headers": {"content-length": "364", "content-type": "application/json"}}
2025-07-14 18:47:41 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900615396100", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:47:41 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900615396100", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.18, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:47:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900744603209", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:47:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524900744616707", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:47:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900744603209", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 36.8, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:47:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524900744616707", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 46.85, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:48:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524901060894136", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&status=inactive", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "status": "inactive"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:48:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524901060894136", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.6, "response_headers": {"content-length": "469", "content-type": "application/json"}}
2025-07-14 18:48:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524901149923573", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:48:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524901149923573", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.03, "response_headers": {"content-length": "1364", "content-type": "application/json"}}
2025-07-14 18:48:45 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524901253626121", "method": "DELETE", "url": "http://localhost:8000/users/17524878600144387", "path": "/users/17524878600144387", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:48:45 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524901253626121", "method": "DELETE", "path": "/users/17524878600144387", "status_code": 200, "process_time_ms": 135.72, "response_headers": {"content-length": "396", "content-type": "application/json"}}
2025-07-14 18:48:45 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524901255051756", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:48:45 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524901255051756", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.34, "response_headers": {"content-length": "1082", "content-type": "application/json"}}
2025-07-14 18:57:00 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524906201229673", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "148", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"username": "user003", "email": "<EMAIL>", "name": "yao zhu", "role": "member", "department": "测试部门", "phone": "15519057432", "password": "123123"}}
2025-07-14 19:03:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524910184428894", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "173", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user003", "email": "<EMAIL>", "name": "yao zhu", "role": "member", "department": "测试部门", "phone": "15519057432", "password": "123123"}}
2025-07-14 19:03:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524910184428894", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 314.88, "response_headers": {"content-length": "615", "content-type": "application/json"}}
2025-07-14 19:03:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524910227737979", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "213", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user004", "email": "<EMAIL>", "name": "新部门测试", "role": "member", "department": "新创建的部门", "phone": "13888888888", "password": "test123"}}
2025-07-14 19:03:43 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524910227737979", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 362.94, "response_headers": {"content-length": "362", "content-type": "application/json"}}
2025-07-14 19:03:47 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524910271445127", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "161", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user005", "email": "<EMAIL>", "name": "无部门用户", "role": "member", "phone": "13777777777", "password": "test123"}}
2025-07-14 19:03:47 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524910271445127", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 317.62, "response_headers": {"content-length": "330", "content-type": "application/json"}}
2025-07-14 19:03:51 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524910314709017", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "218", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user003", "email": "<EMAIL>", "name": "重复用户名测试", "role": "member", "department": "测试部门", "phone": "13666666666", "password": "test123"}}
2025-07-14 19:03:51 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524910314709017", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 238.49, "response_headers": {"content-length": "641", "content-type": "application/json"}}
2025-07-14 19:03:55 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524910357193718", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "115", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"email": "<EMAIL>", "name": "缺少用户名", "role": "member", "password": "test123"}}
2025-07-14 19:03:55 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524910357193718", "method": "POST", "path": "/users/create", "status_code": 422, "process_time_ms": 1.83, "response_headers": {"content-length": "182", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 19:03:59 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524910397294225", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 19:03:59 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524910397294225", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.56, "response_headers": {"content-length": "2251", "content-type": "application/json"}}
2025-07-14 19:05:09 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524911096322149", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "199", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user9247", "email": "<EMAIL>", "name": "测试用户", "role": "member", "department": "测试部门", "phone": "15519057432", "password": "123123"}}
2025-07-14 19:05:09 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524911096322149", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 346.27, "response_headers": {"content-length": "354", "content-type": "application/json"}}
2025-07-14 19:05:13 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524911139911936", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "220", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "test_duplicate_email", "email": "<EMAIL>", "name": "重复邮箱测试", "role": "member", "department": "测试部门", "phone": "15519057432", "password": "123123"}}
2025-07-14 19:05:14 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524911139911936", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 243.75, "response_headers": {"content-length": "72", "content-type": "application/json"}}
2025-07-14 19:05:18 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524911182466677", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 19:05:18 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524911182466677", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.17, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:19:07 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524919479613114", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "148", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"username": "user003", "email": "<EMAIL>", "name": "yao zhu", "role": "member", "department": "测试部门", "phone": "15519057432", "password": "123123"}}
2025-07-14 19:19:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524919479613114", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 278.86, "response_headers": {"content-length": "72", "content-type": "application/json"}}
2025-07-14 19:19:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524919482711498", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:19:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524919482711498", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 27.77, "response_headers": {"content-length": "1980", "content-type": "application/json"}}
2025-07-14 19:20:46 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524920460516860", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:20:46 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524920460529541", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:20:46 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524920460529541", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 17.92, "response_headers": {"content-length": "604", "content-type": "application/json"}}
2025-07-14 19:20:46 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524920460516860", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 20.68, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:21:12 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524920729846696", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:21:12 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524920729858434", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:21:12 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524920729858434", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 13.13, "response_headers": {"content-length": "604", "content-type": "application/json"}}
2025-07-14 19:21:13 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524920729846696", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 18.82, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:21:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524920860612086", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:21:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524920860612086", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.0, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:21:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524920860705038", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:21:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524920860705038", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 3.44, "response_headers": {"content-length": "604", "content-type": "application/json"}}
2025-07-14 19:21:49 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524921092161300", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:21:49 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524921092161300", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.05, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:21:49 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524921092254933", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:21:49 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524921092254933", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.37, "response_headers": {"content-length": "604", "content-type": "application/json"}}
2025-07-14 19:22:01 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524921215034372", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:22:01 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524921215034372", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.03, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:22:01 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524921215106472", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:22:01 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524921215106472", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 3.25, "response_headers": {"content-length": "604", "content-type": "application/json"}}
2025-07-14 19:26:36 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524923968845596", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:36 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524923968845596", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.72, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:26:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524923976167173", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524923976167173", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.81, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:26:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524923989335701", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524923989335701", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.86, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:26:39 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524923997557437", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:39 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524923997557437", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.26, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:26:45 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924051331016", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:45 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924051331016", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 29.25, "response_headers": {"content-length": "1980", "content-type": "application/json"}}
2025-07-14 19:26:52 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924122487834", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&search=%E7%AE%A1%E7%90%86&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "search": "管理", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:52 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924122487834", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.72, "response_headers": {"content-length": "460", "content-type": "application/json"}}
2025-07-14 19:26:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924143497791", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&search=%E7%AE%A1&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "search": "管", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924143497791", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.32, "response_headers": {"content-length": "460", "content-type": "application/json"}}
2025-07-14 19:26:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924148462353", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:26:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924148462353", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.73, "response_headers": {"content-length": "1980", "content-type": "application/json"}}
2025-07-14 19:27:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924361828687", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "130", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"username": "user004", "email": "", "name": "yao zhu", "role": "member", "department": "测试部门", "phone": "15519057432", "password": ""}}
2025-07-14 19:27:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924361828687", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 24.4, "response_headers": {"content-length": "78", "content-type": "application/json"}}
2025-07-14 19:27:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924362573967", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "20", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:27:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924362573967", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.83, "response_headers": {"content-length": "1980", "content-type": "application/json"}}
2025-07-14 19:27:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924400508185", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10&status=active", "path": "/users/list", "query_params": {"page": "1", "page_size": "10", "status": "active"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:27:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924400508185", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 14.92, "response_headers": {"content-length": "1980", "content-type": "application/json"}}
2025-07-14 19:27:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524924547373609", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:27:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524924547373609", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.4, "response_headers": {"content-length": "2559", "content-type": "application/json"}}
2025-07-14 19:28:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925220654460", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10&department=%E6%B5%8B%E8%AF%95%E9%83%A8%E9%97%A8", "path": "/users/list", "query_params": {"page": "1", "page_size": "10", "department": "测试部门"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:28:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925220654460", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 16.21, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 19:28:44 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925247729718", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:28:44 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925247729718", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 11.72, "response_headers": {"content-length": "405", "content-type": "application/json"}}
2025-07-14 19:28:55 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925356075924", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "172", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_no_contact_9677", "name": "无联系方式用户", "role": "member", "department": "测试部门", "password": "123123"}}
2025-07-14 19:28:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925356075924", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 1437.41, "response_headers": {"content-length": "351", "content-type": "application/json"}}
2025-07-14 19:29:01 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925410569395", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "206", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_email_only_3207", "name": "只有邮箱用户", "email": "<EMAIL>", "role": "member", "department": "测试部门", "password": "123123"}}
2025-07-14 19:29:01 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925410569395", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 432.36, "response_headers": {"content-length": "363", "content-type": "application/json"}}
2025-07-14 19:29:05 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925455097093", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "196", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_phone_only_7586", "name": "只有手机号用户", "phone": "13875868888", "role": "member", "department": "测试部门", "password": "123123"}}
2025-07-14 19:29:05 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925455097093", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 321.0, "response_headers": {"content-length": "360", "content-type": "application/json"}}
2025-07-14 19:29:06 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925462826144", "method": "PUT", "url": "http://localhost:8000/users/17524925164951675", "path": "/users/17524925164951675", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "123", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"username": "admin", "email": null, "name": "yao zhu", "role": "admin", "department": "", "phone": "15519057432", "password": "123456"}}
2025-07-14 19:29:06 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925462826144", "method": "PUT", "path": "/users/17524925164951675", "status_code": 200, "process_time_ms": 347.46, "response_headers": {"content-length": "312", "content-type": "application/json"}}
2025-07-14 19:29:06 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925466662317", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:06 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925466662317", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.49, "response_headers": {"content-length": "1355", "content-type": "application/json"}}
2025-07-14 19:29:09 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925498409482", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "201", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_empty_contact_9866", "name": "空联系方式用户", "email": "", "phone": "", "role": "member", "department": "测试部门", "password": "123123"}}
2025-07-14 19:29:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925498409482", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 331.15, "response_headers": {"content-length": "354", "content-type": "application/json"}}
2025-07-14 19:29:14 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925541921607", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "168", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_dup_test1_1355", "name": "重复邮箱测试1", "email": "<EMAIL>", "role": "member", "password": "123123"}}
2025-07-14 19:29:14 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925541921607", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 367.44, "response_headers": {"content-length": "337", "content-type": "application/json"}}
2025-07-14 19:29:18 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925585676258", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "168", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_dup_test2_1355", "name": "重复邮箱测试2", "email": "<EMAIL>", "role": "member", "password": "123123"}}
2025-07-14 19:29:18 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925585676258", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 4.33, "response_headers": {"content-length": "72", "content-type": "application/json"}}
2025-07-14 19:29:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925625793863", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "119", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_no_email_0_1121", "name": "无邮箱用户1", "role": "member", "password": "123123"}}
2025-07-14 19:29:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925625793863", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 342.22, "response_headers": {"content-length": "320", "content-type": "application/json"}}
2025-07-14 19:29:25 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925655728499", "method": "PUT", "url": "http://localhost:8000/users/17524925361491496", "path": "/users/17524925361491496", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "154", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"username": "user_no_contact_9677", "email": null, "name": "无联系方式用户", "role": "member", "department": "测试部门", "phone": "", "password": "123456"}}
2025-07-14 19:29:25 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925655728499", "method": "PUT", "path": "/users/17524925361491496", "status_code": 200, "process_time_ms": 340.25, "response_headers": {"content-length": "355", "content-type": "application/json"}}
2025-07-14 19:29:25 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925659432079", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:25 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925659432079", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.55, "response_headers": {"content-length": "2226", "content-type": "application/json"}}
2025-07-14 19:29:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925669292222", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "119", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_no_email_1_3905", "name": "无邮箱用户2", "role": "member", "password": "123123"}}
2025-07-14 19:29:27 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925669292222", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 344.28, "response_headers": {"content-length": "320", "content-type": "application/json"}}
2025-07-14 19:29:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925698845324", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925698845324", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.32, "response_headers": {"content-length": "2500", "content-type": "application/json"}}
2025-07-14 19:29:30 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925707542412", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:30 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925707542412", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.42, "response_headers": {"content-length": "2500", "content-type": "application/json"}}
2025-07-14 19:29:31 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925712865020", "method": "POST", "url": "http://localhost:8000/users/create", "path": "/users/create", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive", "content-length": "119", "content-type": "application/json"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": {"username": "user_no_email_2_1257", "name": "无邮箱用户3", "role": "member", "password": "123123"}}
2025-07-14 19:29:31 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925714545172", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:31 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925714545172", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.19, "response_headers": {"content-length": "2500", "content-type": "application/json"}}
2025-07-14 19:29:31 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925712865020", "method": "POST", "path": "/users/create", "status_code": 200, "process_time_ms": 334.9, "response_headers": {"content-length": "320", "content-type": "application/json"}}
2025-07-14 19:29:31 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925716651012", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:31 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925716651012", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.07, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925721492662", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925721492662", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.28, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925725203823", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925725203823", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.82, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925726907839", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925726907839", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.86, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925728501491", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:32 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925728501491", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.99, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:33 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925733038741", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:33 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925733038741", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.7, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:33 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925737896513", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:33 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925737896513", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.99, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:33 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925739746126", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:33 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925739746126", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.28, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925741741058", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925741741058", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.32, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925743716130", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925743716130", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.19, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925745798195", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925745798195", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.91, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925747596895", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925747596895", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.12, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925749407517", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925749407517", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.64, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925751556055", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925751556055", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.79, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925753853707", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925753853707", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.87, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925756282871", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925756282871", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.41, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925756535645", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925756535645", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.59, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925758746945", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925758746945", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.49, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925760737341", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925760737341", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.0, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925762501876", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925762501876", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.7, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925764206565", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925764206565", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.09, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925765922028", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925765922028", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.66, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925768509432", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:36 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925768509432", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.5, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925770192868", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925770192868", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.96, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925772034415", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925772034415", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.95, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925773539159", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925773539159", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.06, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925774765459", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925774765459", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.61, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925777301090", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925777301090", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.69, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
2025-07-14 19:29:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524925809374617", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=10", "path": "/users/list", "query_params": {"page": "1", "page_size": "10"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 19:29:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524925809374617", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.52, "response_headers": {"content-length": "2774", "content-type": "application/json"}}
