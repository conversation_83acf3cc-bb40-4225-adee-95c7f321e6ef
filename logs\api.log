2025-07-14 16:11:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524807143423963", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:11:55 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524807143423963", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 772.09, "response_headers": {"content-length": "465", "content-type": "application/json"}}
2025-07-14 16:15:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524809101348963", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:15:10 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:15:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524809101348963", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 288.08, "response_headers": {"content-length": "465", "content-type": "application/json"}}
2025-07-14 16:15:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524809280176280", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:15:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524809280176280", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 3.36, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:15:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524809372746886", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:15:37 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:15:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524809372746886", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 264.7, "response_headers": {"content-length": "465", "content-type": "application/json"}}
2025-07-14 16:17:11 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524810316824121", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:17:11 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524810316824121", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.89, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:17:24 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524810441748092", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:17:24 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:17:24 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524810441748092", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 252.27, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:19:41 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524811813555193", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:19:41 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524811813555193", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.71, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:19:56 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524811961382487", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:19:56 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:19:56 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524811961382487", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 244.91, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:21:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524813177208782", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:21:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524813177208782", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 4.62, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:22:06 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524813263222755", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:22:06 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:22:06 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524813263222755", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 238.06, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:22:12 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524813322465803", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:22:12 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524813322465803", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 6.05, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:29:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524817624309434", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:29:22 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:29:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524817624309434", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 244.64, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:30:00 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524818007626377", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:30:00 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524818007626377", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.04, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 16:30:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524818229771607", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 16:30:23 | api | INFO | users.py:118 | 用户登录成功: admin from 127.0.0.1
2025-07-14 16:30:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524818229771607", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 240.57, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 16:44:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524826660689644", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752482193207", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8080", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8080/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 16:44:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524826660689644", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.87, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:20:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524848283889342", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752483963008", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8080", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8080/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:20:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524848283889342", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 14.36, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:22:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849495265553", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:22:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849495265553", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.9, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:22:35 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849556124322", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:22:35 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849556124322", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 295.91, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:22:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849772534388", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:22:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849772534388", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.56, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:23:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524849881571283", "method": "POST", "url": "http://localhost:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "40", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:23:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524849881571283", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 265.39, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:24:04 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850448149447", "method": "POST", "url": "http://127.0.0.1:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "43", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:24:05 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850448149447", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 293.41, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:24:05 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850451112036", "method": "POST", "url": "http://127.0.0.1:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "0", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlkIjoiMTc1MjQ3ODMwMjQyNTAwMDEiLCJleHAiOjE3NTI0ODY4NDV9.q6OWN7ZJfJKaN8sjb4cj85e7Oa5VqfKQHZpXu1EFkCA", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": "authenticated_user", "body": null}
2025-07-14 17:24:05 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850451112036", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 36.51, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850900232106", "method": "POST", "url": "http://127.0.0.1:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "43", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850900232106", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 243.8, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524850902771432", "method": "POST", "url": "http://127.0.0.1:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "0", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlkIjoiMTc1MjQ3ODMwMjQyNTAwMDEiLCJleHAiOjE3NTI0ODY4OTB9.EBtpM7Oy08jMx5H1YHW-plaT18v42pXsT7kmd8GpEkY", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": "authenticated_user", "body": null}
2025-07-14 17:24:50 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524850902771432", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.57, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524851296906819", "method": "POST", "url": "http://127.0.0.1:8000/users/login", "path": "/users/login", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "43", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": null, "body": {"username": "admin", "password": "123456"}}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524851296906819", "method": "POST", "path": "/users/login", "status_code": 200, "process_time_ms": 266.28, "response_headers": {"content-length": "397", "content-type": "application/json"}}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524851299679249", "method": "POST", "url": "http://127.0.0.1:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"accept-encoding": "identity", "content-length": "0", "host": "127.0.0.1:8000", "user-agent": "Python-urllib/3.13", "content-type": "application/json", "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImlkIjoiMTc1MjQ3ODMwMjQyNTAwMDEiLCJleHAiOjE3NTI0ODY5Mjl9.5FBx4TEhT9Ic23OsrKGdVUfOUCt3qDRy5Y8LcBv308A", "connection": "close"}, "client_ip": "127.0.0.1", "user_agent": "Python-urllib/3.13", "user_id": "authenticated_user", "body": null}
2025-07-14 17:25:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524851299679249", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 6.9, "response_headers": {"content-length": "61", "content-type": "application/json"}}
2025-07-14 17:26:07 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524851677935217", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752484838075", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8080", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8080/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:26:07 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524851677935217", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.56, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:31:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524855008072187", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer default-token", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:31:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524855008072187", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 2.46, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:35:45 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524857451865277", "method": "POST", "url": "http://localhost:8000/users/logout", "path": "/users/logout", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "0", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485515921", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:35:45 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524857451865277", "method": "POST", "path": "/users/logout", "status_code": 200, "process_time_ms": 1.98, "response_headers": {"content-length": "45", "content-type": "application/json"}}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870026457620", "method": "GET", "url": "http://localhost:8000/users?page=1&pageSize=20", "path": "/users", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870026479840", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870026457620", "method": "GET", "path": "/users", "status_code": 404, "process_time_ms": 2.6, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:56:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870026479840", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 2.51, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:57:00 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870201467784", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:00 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870201467784", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 15.1, "response_headers": {"content-length": "526", "content-type": "application/json"}}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870228755900", "method": "GET", "url": "http://localhost:8000/users?page=1&pageSize=20", "path": "/users", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870228755900", "method": "GET", "path": "/users", "status_code": 404, "process_time_ms": 2.08, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870228797065", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 17:57:02 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870228797065", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 1.05, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 17:57:04 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870241699105", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:04 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870241699105", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.17, "response_headers": {"content-length": "525", "content-type": "application/json"}}
2025-07-14 17:57:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870281827649", "method": "GET", "url": "http://localhost:8000/users/list?search=test&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "test", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870281827649", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.32, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:57:12 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870321971854", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:12 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870321971854", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.7, "response_headers": {"content-length": "342", "content-type": "application/json"}}
2025-07-14 17:57:16 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870362161621", "method": "GET", "url": "http://localhost:8000/users/list?status=active&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "active", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:57:16 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870362161621", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.89, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:58:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870957047193", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870957047193", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.74, "response_headers": {"content-length": "540", "content-type": "application/json"}}
2025-07-14 17:58:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524870997229561", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524870997229561", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.22, "response_headers": {"content-length": "539", "content-type": "application/json"}}
2025-07-14 17:58:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871037353022", "method": "GET", "url": "http://localhost:8000/users/list?search=test&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "test", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871037353022", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.38, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:58:27 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871077528981", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:27 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871077528981", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.8, "response_headers": {"content-length": "349", "content-type": "application/json"}}
2025-07-14 17:58:31 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871117676828", "method": "GET", "url": "http://localhost:8000/users/list?status=active&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "active", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:58:31 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871117676828", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.44, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:59:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871623456018", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871623456018", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 8.54, "response_headers": {"content-length": "540", "content-type": "application/json"}}
2025-07-14 17:59:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871663691434", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871663691434", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.41, "response_headers": {"content-length": "539", "content-type": "application/json"}}
2025-07-14 17:59:30 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871703874081", "method": "GET", "url": "http://localhost:8000/users/list?search=test&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "test", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:30 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871703874081", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.87, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 17:59:34 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871744007684", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:34 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871744007684", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.03, "response_headers": {"content-length": "349", "content-type": "application/json"}}
2025-07-14 17:59:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871784151989", "method": "GET", "url": "http://localhost:8000/users/list?status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871784151989", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.14, "response_headers": {"content-length": "540", "content-type": "application/json"}}
2025-07-14 17:59:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524871824275423", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 17:59:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524871824275423", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.31, "response_headers": {"content-length": "349", "content-type": "application/json"}}
2025-07-14 18:11:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524878980019032", "method": "GET", "url": "http://localhost:8000/users/list", "path": "/users/list", "query_params": {}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524878980019032", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.54, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:11:42 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879020211781", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=5", "path": "/users/list", "query_params": {"page": "1", "pageSize": "5"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:42 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879020211781", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.51, "response_headers": {"content-length": "1673", "content-type": "application/json"}}
2025-07-14 18:11:46 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879060347928", "method": "GET", "url": "http://localhost:8000/users/list?search=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"search": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:46 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879060347928", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.34, "response_headers": {"content-length": "460", "content-type": "application/json"}}
2025-07-14 18:11:50 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879100487894", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:50 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879100487894", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.48, "response_headers": {"content-length": "460", "content-type": "application/json"}}
2025-07-14 18:11:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879140631992", "method": "GET", "url": "http://localhost:8000/users/list?status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879140631992", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.93, "response_headers": {"content-length": "469", "content-type": "application/json"}}
2025-07-14 18:11:58 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879180869499", "method": "GET", "url": "http://localhost:8000/users/list?role=admin&status=inactive&page=1&pageSize=10", "path": "/users/list", "query_params": {"role": "admin", "status": "inactive", "page": "1", "pageSize": "10"}, "headers": {"host": "localhost:8000", "user-agent": "python-requests/2.32.4", "accept-encoding": "gzip, deflate", "accept": "*/*", "connection": "keep-alive"}, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.4", "user_id": null, "body": null}
2025-07-14 18:11:58 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879180869499", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.45, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879584179385", "method": "GET", "url": "http://localhost:8000/users?page=1&pageSize=20", "path": "/users", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524879584198758", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879584179385", "method": "GET", "path": "/users", "status_code": 404, "process_time_ms": 3.05, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:12:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524879584198758", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 2.45, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:13:43 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880232046286", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:13:43 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880232046286", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 33.54, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:14:24 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880641946643", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&search=admin&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "search": "admin", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:24 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880641946643", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 20.5, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:14:48 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880886499733", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&search=0&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "search": "0", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:48 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880886499733", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.56, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:14:54 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880947039638", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&search=0&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "search": "0", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:54 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880947039638", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.84, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:14:59 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524880994119083", "method": "GET", "url": "http://127.0.0.1:8000/users/list?page=1&pageSize=20&role=1&status=0", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20", "role": "1", "status": "0"}, "headers": {"host": "127.0.0.1:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-fetch-site": "same-origin", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://127.0.0.1:8000/docs", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9", "cookie": "csrftoken=yCSUfsOvu1DpHvzKrZ2HHGwqTGljCdsO"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": null, "body": null}
2025-07-14 18:14:59 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524880994119083", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 8.8, "response_headers": {"content-length": "160", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881403676476", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881403837377", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881403676476", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 25.84, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881403837377", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 68.82, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881404924293", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881405277097", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881404924293", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 233.68, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881405277097", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 228.32, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881407725694", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:15:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881407725694", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.36, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756165253", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756187619", "method": "GET", "url": "http://localhost:8000/departments/list", "path": "/departments/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756187619", "method": "GET", "path": "/departments/list", "status_code": 404, "process_time_ms": 1.71, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756165253", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.03, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756841242", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881756873848", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756841242", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 11.6, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881756873848", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 18.93, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524881757749051", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:16:15 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524881757749051", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.17, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882881524623", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882881549301", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882881524623", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 73.22, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882881549301", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 72.22, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882882759072", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882882777939", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882882777939", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 8.94, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882882759072", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 31.3, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882883317651", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:08 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882883317651", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.23, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882995995731", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882995995731", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.55, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882996074107", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882996074107", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 7.67, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882996808448", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882996808448", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.84, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882997108863", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882997108863", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.67, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524882997524827", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:19 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524882997524827", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 13.8, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883092611088", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883092611088", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.2, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883093224276", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883093257404", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883093257404", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 16.27, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883093224276", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 37.88, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883093641187", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:29 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883093641187", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 6.12, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883175133333", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883175133333", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.28, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883175218962", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883175218962", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.24, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883176126143", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883176126143", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 11.07, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883176269949", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883176269949", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 13.97, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883176429683", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883176429683", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 12.24, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883375923983", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883375938214", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883375923983", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 51.24, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883375938214", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 60.75, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883376866978", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883376899015", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883376899015", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 17.66, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883376866978", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 41.88, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883377496071", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:18:57 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883377496071", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 53.5, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883507666571", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883507666571", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.01, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883507734349", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883507734349", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.76, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883508055229", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883508055229", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.12, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883508199321", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883508199321", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 5.63, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883508693304", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:10 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883508693304", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 14.48, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883662582605", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883662611224", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883662611224", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 93.53, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883662582605", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 100.75, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883663866609", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883663876442", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883663876442", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 20.47, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883663866609", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 31.11, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883664263598", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:26 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883664263598", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 9.99, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883801633795", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883801693279", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883801633795", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 66.95, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883801693279", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 65.61, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883803019734", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883803034176", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883803034176", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 10.31, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883803019734", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 19.61, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524883803344286", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:19:40 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524883803344286", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 12.51, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884209368327", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884209368327", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 4.53, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884209432461", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884209432461", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 4.35, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884210057503", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884210057503", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.97, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884210121150", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884210121150", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 7.72, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884210949990", "method": "GET", "url": "http://localhost:8000/users/list?pageSize=20", "path": "/users/list", "query_params": {"pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:20:21 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884210949990", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.76, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884825665259", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524884825683966", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884825683966", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 7.31, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:21:22 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524884825665259", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 16.71, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885138347124", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885138347124", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.15, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885138755182", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:21:53 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885138755182", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 12.94, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885331731402", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885331731402", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.92, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885331809634", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:13 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885331809634", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 5.23, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885438164348", "method": "GET", "url": "http://localhost:8000/users/list?page=1&pageSize=20", "path": "/users/list", "query_params": {"page": "1", "pageSize": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885438164348", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 3.61, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885438228623", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:23 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885438228623", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 9.74, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885577013023", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885577013023", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 8.38, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885577661261", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:22:37 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885577661261", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 63.13, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:22:43 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524885637189429", "method": "PATCH", "url": "http://localhost:8000/users/17524878596987671/status", "path": "/users/17524878596987671/status", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "content-length": "19", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "content-type": "application/json", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": {"status": "active"}}
2025-07-14 18:22:43 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524885637189429", "method": "PATCH", "path": "/users/17524878596987671/status", "status_code": 404, "process_time_ms": 1.41, "response_headers": {"content-length": "22", "content-type": "application/json"}, "response_body": "Error response"}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887206109777", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887206114687", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887206114687", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 18.28, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:20 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887206109777", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.52, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887288608265", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887288608265", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 61.45, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887289752099", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:28 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887289752099", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 11.42, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887389336767", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887389351448", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887389336767", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 53.46, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
2025-07-14 18:25:38 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887389351448", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 58.46, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887488527253", "method": "GET", "url": "http://localhost:8000/users/list?page=1&page_size=20", "path": "/users/list", "query_params": {"page": "1", "page_size": "20"}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:135 | Request started: {"request_id": "REQ17524887488539157", "method": "GET", "url": "http://localhost:8000/organizations/list", "path": "/organizations/list", "query_params": {}, "headers": {"host": "localhost:8000", "connection": "keep-alive", "sec-ch-ua-platform": "\"Windows\"", "authorization": "Bearer mock-token-1752485774776", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "application/json, text/plain, */*", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "origin": "http://localhost:8081", "sec-fetch-site": "same-site", "sec-fetch-mode": "cors", "sec-fetch-dest": "empty", "referer": "http://localhost:8081/", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "zh-CN,zh;q=0.9"}, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "user_id": "authenticated_user", "body": null}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887488539157", "method": "GET", "path": "/organizations/list", "status_code": 200, "process_time_ms": 16.43, "response_headers": {"content-length": "316", "content-type": "application/json"}}
2025-07-14 18:25:48 | api | INFO | logging_middleware.py:160 | Request completed: {"request_id": "REQ17524887488527253", "method": "GET", "path": "/users/list", "status_code": 200, "process_time_ms": 21.74, "response_headers": {"content-length": "1674", "content-type": "application/json"}}
