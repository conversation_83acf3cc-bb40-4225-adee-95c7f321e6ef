#!/usr/bin/env python3
"""
用户表字段迁移脚本
添加姓名、邮箱、电话字段
"""

import sqlite3
import os

def migrate_user_table():
    """迁移用户表，添加新字段"""
    db_path = "project_management.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"当前用户表字段: {columns}")
        
        # 添加新字段（如果不存在）
        new_fields = [
            ("name", "VARCHAR(100)"),
            ("email", "VARCHAR(100)"),
            ("phone", "VARCHAR(20)")
        ]
        
        for field_name, field_type in new_fields:
            if field_name not in columns:
                try:
                    sql = f"ALTER TABLE users ADD COLUMN {field_name} {field_type}"
                    cursor.execute(sql)
                    print(f"✅ 成功添加字段: {field_name}")
                except sqlite3.Error as e:
                    print(f"❌ 添加字段 {field_name} 失败: {e}")
            else:
                print(f"⚠️  字段 {field_name} 已存在，跳过")
        
        # 为email字段创建唯一索引（如果不存在）
        try:
            cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users(email)")
            print("✅ 成功创建email唯一索引")
        except sqlite3.Error as e:
            print(f"❌ 创建email索引失败: {e}")
        
        # 提交更改
        conn.commit()
        
        # 验证更改
        cursor.execute("PRAGMA table_info(users)")
        updated_columns = [column[1] for column in cursor.fetchall()]
        print(f"更新后用户表字段: {updated_columns}")
        
        conn.close()
        print("✅ 用户表迁移完成")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

def update_existing_users():
    """更新现有用户数据，设置默认值"""
    db_path = "project_management.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询现有用户
        cursor.execute("SELECT id, username FROM users WHERE name IS NULL OR name = ''")
        users = cursor.fetchall()
        
        print(f"找到 {len(users)} 个需要更新的用户")
        
        # 为没有姓名的用户设置默认姓名
        for user_id, username in users:
            try:
                # 使用用户名作为默认姓名
                cursor.execute("UPDATE users SET name = ? WHERE id = ?", (username, user_id))
                print(f"✅ 更新用户 {username} 的姓名")
            except sqlite3.Error as e:
                print(f"❌ 更新用户 {username} 失败: {e}")
        
        conn.commit()
        conn.close()
        print("✅ 用户数据更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新用户数据失败: {e}")
        return False

def main():
    """执行迁移"""
    print("开始用户表字段迁移...")
    
    # 1. 迁移表结构
    if migrate_user_table():
        print("\n表结构迁移成功")
    else:
        print("\n表结构迁移失败")
        return
    
    # 2. 更新现有数据
    print("\n开始更新现有用户数据...")
    if update_existing_users():
        print("\n用户数据更新成功")
    else:
        print("\n用户数据更新失败")
    
    print("\n=== 迁移完成 ===")

if __name__ == "__main__":
    main()
