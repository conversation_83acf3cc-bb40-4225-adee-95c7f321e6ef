2025-07-14 16:11:55 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 772.09, "status_code": 200}
2025-07-14 16:15:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 288.08, "status_code": 200}
2025-07-14 16:15:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 3.36, "status_code": 200}
2025-07-14 16:15:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 264.7, "status_code": 200}
2025-07-14 16:17:11 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.89, "status_code": 200}
2025-07-14 16:17:24 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 252.27, "status_code": 200}
2025-07-14 16:19:41 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.71, "status_code": 200}
2025-07-14 16:19:56 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 244.91, "status_code": 200}
2025-07-14 16:21:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 4.62, "status_code": 200}
2025-07-14 16:22:06 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 238.06, "status_code": 200}
2025-07-14 16:22:12 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 6.05, "status_code": 200}
2025-07-14 16:29:22 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 244.64, "status_code": 200}
2025-07-14 16:30:00 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.04, "status_code": 200}
2025-07-14 16:30:23 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 240.57, "status_code": 200}
2025-07-14 16:44:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.87, "status_code": 200}
2025-07-14 17:20:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 14.36, "status_code": 200}
2025-07-14 17:22:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.9, "status_code": 200}
2025-07-14 17:22:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 295.91, "status_code": 200}
2025-07-14 17:22:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.56, "status_code": 200}
2025-07-14 17:23:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 265.39, "status_code": 200}
2025-07-14 17:24:05 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 293.41, "status_code": 200}
2025-07-14 17:24:05 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 36.51, "status_code": 200}
2025-07-14 17:24:50 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 243.8, "status_code": 200}
2025-07-14 17:24:50 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.57, "status_code": 200}
2025-07-14 17:25:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 266.28, "status_code": 200}
2025-07-14 17:25:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 6.9, "status_code": 200}
2025-07-14 17:26:07 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.56, "status_code": 200}
2025-07-14 17:31:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.46, "status_code": 200}
2025-07-14 17:35:45 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.98, "status_code": 200}
