2025-07-14 16:11:55 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 772.09, "status_code": 200}
2025-07-14 16:15:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 288.08, "status_code": 200}
2025-07-14 16:15:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 3.36, "status_code": 200}
2025-07-14 16:15:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 264.7, "status_code": 200}
2025-07-14 16:17:11 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.89, "status_code": 200}
2025-07-14 16:17:24 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 252.27, "status_code": 200}
2025-07-14 16:19:41 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.71, "status_code": 200}
2025-07-14 16:19:56 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 244.91, "status_code": 200}
2025-07-14 16:21:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 4.62, "status_code": 200}
2025-07-14 16:22:06 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 238.06, "status_code": 200}
2025-07-14 16:22:12 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 6.05, "status_code": 200}
2025-07-14 16:29:22 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 244.64, "status_code": 200}
2025-07-14 16:30:00 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.04, "status_code": 200}
2025-07-14 16:30:23 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 240.57, "status_code": 200}
2025-07-14 16:44:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.87, "status_code": 200}
2025-07-14 17:20:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 14.36, "status_code": 200}
2025-07-14 17:22:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.9, "status_code": 200}
2025-07-14 17:22:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 295.91, "status_code": 200}
2025-07-14 17:22:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.56, "status_code": 200}
2025-07-14 17:23:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 265.39, "status_code": 200}
2025-07-14 17:24:05 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 293.41, "status_code": 200}
2025-07-14 17:24:05 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 36.51, "status_code": 200}
2025-07-14 17:24:50 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 243.8, "status_code": 200}
2025-07-14 17:24:50 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.57, "status_code": 200}
2025-07-14 17:25:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/login", "process_time_ms": 266.28, "status_code": 200}
2025-07-14 17:25:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 6.9, "status_code": 200}
2025-07-14 17:26:07 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.56, "status_code": 200}
2025-07-14 17:31:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 2.46, "status_code": 200}
2025-07-14 17:35:45 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/logout", "process_time_ms": 1.98, "status_code": 200}
2025-07-14 17:56:42 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users", "process_time_ms": 2.6, "status_code": 404}
2025-07-14 17:56:42 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/departments/list", "process_time_ms": 2.51, "status_code": 404}
2025-07-14 17:57:00 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 15.1, "status_code": 200}
2025-07-14 17:57:02 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users", "process_time_ms": 2.08, "status_code": 404}
2025-07-14 17:57:02 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/departments/list", "process_time_ms": 1.05, "status_code": 404}
2025-07-14 17:57:04 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.17, "status_code": 200}
2025-07-14 17:57:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.32, "status_code": 200}
2025-07-14 17:57:12 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.7, "status_code": 200}
2025-07-14 17:57:16 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.89, "status_code": 200}
2025-07-14 17:58:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.74, "status_code": 200}
2025-07-14 17:58:19 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.22, "status_code": 200}
2025-07-14 17:58:23 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.38, "status_code": 200}
2025-07-14 17:58:27 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.8, "status_code": 200}
2025-07-14 17:58:31 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.44, "status_code": 200}
2025-07-14 17:59:22 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 8.54, "status_code": 200}
2025-07-14 17:59:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.41, "status_code": 200}
2025-07-14 17:59:30 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.87, "status_code": 200}
2025-07-14 17:59:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.03, "status_code": 200}
2025-07-14 17:59:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.14, "status_code": 200}
2025-07-14 17:59:42 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.31, "status_code": 200}
2025-07-14 18:11:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.54, "status_code": 200}
2025-07-14 18:11:42 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.51, "status_code": 200}
2025-07-14 18:11:46 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.34, "status_code": 200}
2025-07-14 18:11:50 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.48, "status_code": 200}
2025-07-14 18:11:54 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.93, "status_code": 200}
2025-07-14 18:11:58 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.45, "status_code": 200}
2025-07-14 18:12:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users", "process_time_ms": 3.05, "status_code": 404}
2025-07-14 18:12:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/departments/list", "process_time_ms": 2.45, "status_code": 404}
2025-07-14 18:13:43 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 33.54, "status_code": 200}
2025-07-14 18:14:24 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 20.5, "status_code": 200}
2025-07-14 18:14:48 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.56, "status_code": 200}
2025-07-14 18:14:54 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.84, "status_code": 200}
2025-07-14 18:14:59 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 8.8, "status_code": 200}
2025-07-14 18:15:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/departments/list", "process_time_ms": 25.84, "status_code": 404}
2025-07-14 18:15:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 68.82, "status_code": 200}
2025-07-14 18:15:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 233.68, "status_code": 200}
2025-07-14 18:15:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 228.32, "status_code": 200}
2025-07-14 18:15:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.36, "status_code": 200}
2025-07-14 18:16:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/departments/list", "process_time_ms": 1.71, "status_code": 404}
2025-07-14 18:16:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.03, "status_code": 200}
2025-07-14 18:16:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 11.6, "status_code": 200}
2025-07-14 18:16:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 18.93, "status_code": 200}
2025-07-14 18:16:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.17, "status_code": 200}
2025-07-14 18:18:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 73.22, "status_code": 200}
2025-07-14 18:18:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 72.22, "status_code": 200}
2025-07-14 18:18:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 8.94, "status_code": 200}
2025-07-14 18:18:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 31.3, "status_code": 200}
2025-07-14 18:18:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.23, "status_code": 200}
2025-07-14 18:18:19 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.55, "status_code": 200}
2025-07-14 18:18:19 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 7.67, "status_code": 200}
2025-07-14 18:18:19 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.84, "status_code": 200}
2025-07-14 18:18:19 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 21.67, "status_code": 200}
2025-07-14 18:18:19 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 13.8, "status_code": 200}
2025-07-14 18:18:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.2, "status_code": 200}
2025-07-14 18:18:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 16.27, "status_code": 200}
2025-07-14 18:18:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 37.88, "status_code": 200}
2025-07-14 18:18:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.12, "status_code": 200}
2025-07-14 18:18:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.28, "status_code": 200}
2025-07-14 18:18:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.24, "status_code": 200}
2025-07-14 18:18:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 11.07, "status_code": 200}
2025-07-14 18:18:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 13.97, "status_code": 200}
2025-07-14 18:18:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 12.24, "status_code": 200}
2025-07-14 18:18:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 51.24, "status_code": 200}
2025-07-14 18:18:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 60.75, "status_code": 200}
2025-07-14 18:18:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 17.66, "status_code": 200}
2025-07-14 18:18:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 41.88, "status_code": 200}
2025-07-14 18:18:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 53.5, "status_code": 200}
2025-07-14 18:19:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.01, "status_code": 200}
2025-07-14 18:19:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.76, "status_code": 200}
2025-07-14 18:19:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.12, "status_code": 200}
2025-07-14 18:19:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.63, "status_code": 200}
2025-07-14 18:19:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 14.48, "status_code": 200}
2025-07-14 18:19:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 93.53, "status_code": 200}
2025-07-14 18:19:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 100.75, "status_code": 200}
2025-07-14 18:19:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 20.47, "status_code": 200}
2025-07-14 18:19:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 31.11, "status_code": 200}
2025-07-14 18:19:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.99, "status_code": 200}
2025-07-14 18:19:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 66.95, "status_code": 200}
2025-07-14 18:19:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 65.61, "status_code": 200}
2025-07-14 18:19:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.31, "status_code": 200}
2025-07-14 18:19:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 19.61, "status_code": 200}
2025-07-14 18:19:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 12.51, "status_code": 200}
2025-07-14 18:20:20 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.53, "status_code": 200}
2025-07-14 18:20:20 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.35, "status_code": 200}
2025-07-14 18:20:21 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.97, "status_code": 200}
2025-07-14 18:20:21 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.72, "status_code": 200}
2025-07-14 18:20:21 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.76, "status_code": 200}
2025-07-14 18:21:22 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 7.31, "status_code": 200}
2025-07-14 18:21:22 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 16.71, "status_code": 200}
2025-07-14 18:21:53 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 21.15, "status_code": 200}
2025-07-14 18:21:53 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 12.94, "status_code": 200}
2025-07-14 18:22:13 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.92, "status_code": 200}
2025-07-14 18:22:13 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 5.23, "status_code": 200}
2025-07-14 18:22:23 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.61, "status_code": 200}
2025-07-14 18:22:23 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 9.74, "status_code": 200}
2025-07-14 18:22:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 8.38, "status_code": 200}
2025-07-14 18:22:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 63.13, "status_code": 200}
2025-07-14 18:22:43 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "PATCH", "path": "/users/17524878596987671/status", "process_time_ms": 1.41, "status_code": 404}
2025-07-14 18:25:20 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 18.28, "status_code": 200}
2025-07-14 18:25:20 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 21.52, "status_code": 200}
2025-07-14 18:25:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 61.45, "status_code": 200}
2025-07-14 18:25:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 11.42, "status_code": 200}
2025-07-14 18:25:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 53.46, "status_code": 200}
2025-07-14 18:25:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 58.46, "status_code": 200}
2025-07-14 18:25:48 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 16.43, "status_code": 200}
2025-07-14 18:25:48 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 21.74, "status_code": 200}
2025-07-14 18:26:03 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 18.79, "status_code": 200}
2025-07-14 18:26:03 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 21.32, "status_code": 200}
2025-07-14 18:26:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.01, "status_code": 200}
2025-07-14 18:26:28 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 10.42, "status_code": 200}
2025-07-14 18:26:45 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.85, "status_code": 200}
2025-07-14 18:26:45 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.67, "status_code": 200}
2025-07-14 18:28:30 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 5.58, "status_code": 200}
2025-07-14 18:28:30 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.35, "status_code": 200}
2025-07-14 18:29:01 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.35, "status_code": 200}
2025-07-14 18:29:01 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 5.51, "status_code": 200}
2025-07-14 18:29:13 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.85, "status_code": 200}
2025-07-14 18:29:13 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 12.53, "status_code": 200}
2025-07-14 18:29:24 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.43, "status_code": 200}
2025-07-14 18:29:24 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 43.28, "status_code": 200}
2025-07-14 18:30:43 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 5.5, "status_code": 200}
2025-07-14 18:30:43 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 8.08, "status_code": 200}
2025-07-14 18:32:05 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.41, "status_code": 200}
2025-07-14 18:32:05 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 8.99, "status_code": 200}
2025-07-14 18:32:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.46, "status_code": 200}
2025-07-14 18:32:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 3.31, "status_code": 200}
2025-07-14 18:32:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.19, "status_code": 200}
2025-07-14 18:32:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 10.33, "status_code": 200}
2025-07-14 18:32:46 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/deactivate", "process_time_ms": 83.4, "status_code": 200}
2025-07-14 18:32:46 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.17, "status_code": 200}
2025-07-14 18:32:52 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/activate", "process_time_ms": 102.28, "status_code": 200}
2025-07-14 18:32:52 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.35, "status_code": 200}
2025-07-14 18:34:23 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 5.57, "status_code": 200}
2025-07-14 18:34:23 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.53, "status_code": 200}
2025-07-14 18:34:39 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.3, "status_code": 200}
2025-07-14 18:34:39 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.53, "status_code": 200}
2025-07-14 18:35:16 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.35, "status_code": 200}
2025-07-14 18:35:16 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 56.33, "status_code": 200}
2025-07-14 18:35:17 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 63.75, "status_code": 200}
2025-07-14 18:35:17 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 68.75, "status_code": 200}
2025-07-14 18:35:51 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.53, "status_code": 200}
2025-07-14 18:35:51 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.44, "status_code": 200}
2025-07-14 18:36:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.58, "status_code": 200}
2025-07-14 18:36:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 7.03, "status_code": 200}
2025-07-14 18:36:56 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 31.55, "status_code": 200}
2025-07-14 18:36:56 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 10.98, "status_code": 200}
2025-07-14 18:37:11 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 19.42, "status_code": 200}
2025-07-14 18:37:11 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.95, "status_code": 200}
2025-07-14 18:37:25 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.34, "status_code": 200}
2025-07-14 18:37:25 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 6.84, "status_code": 200}
2025-07-14 18:37:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/activate", "process_time_ms": 634.12, "status_code": 200}
2025-07-14 18:37:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 21.53, "status_code": 200}
2025-07-14 18:37:32 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/deactivate", "process_time_ms": 99.45, "status_code": 200}
2025-07-14 18:37:32 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.69, "status_code": 200}
2025-07-14 18:37:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.98, "status_code": 200}
2025-07-14 18:37:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 8.95, "status_code": 200}
2025-07-14 18:39:47 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.68, "status_code": 200}
2025-07-14 18:39:47 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 11.23, "status_code": 200}
2025-07-14 18:40:01 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.0, "status_code": 200}
2025-07-14 18:40:01 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 17.4, "status_code": 200}
2025-07-14 18:40:14 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "PUT", "path": "/users/17524878590921916", "process_time_ms": 1.6, "status_code": 405}
2025-07-14 18:40:16 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.97, "status_code": 200}
2025-07-14 18:40:16 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 6.05, "status_code": 200}
2025-07-14 18:40:30 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.8, "status_code": 200}
2025-07-14 18:40:30 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 3.93, "status_code": 200}
2025-07-14 18:40:42 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.58, "status_code": 200}
2025-07-14 18:40:42 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 5.73, "status_code": 200}
2025-07-14 18:40:56 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.92, "status_code": 200}
2025-07-14 18:40:56 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 6.5, "status_code": 200}
2025-07-14 18:45:53 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 6.26, "status_code": 200}
2025-07-14 18:45:53 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.96, "status_code": 200}
2025-07-14 18:46:02 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.97, "status_code": 200}
2025-07-14 18:46:02 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.19, "status_code": 200}
2025-07-14 18:46:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.28, "status_code": 200}
2025-07-14 18:46:15 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.35, "status_code": 200}
2025-07-14 18:46:50 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "PATCH", "path": "/users/17524878594012259/status", "process_time_ms": 1.91, "status_code": 405}
2025-07-14 18:47:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 138.18, "status_code": 200}
2025-07-14 18:47:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 170.87, "status_code": 200}
2025-07-14 18:47:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "PUT", "path": "/users/17524878594012259/status", "process_time_ms": 113.5, "status_code": 200}
2025-07-14 18:47:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 16.84, "status_code": 200}
2025-07-14 18:47:41 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "PUT", "path": "/users/17524878594012259/status", "process_time_ms": 161.44, "status_code": 200}
2025-07-14 18:47:41 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 21.18, "status_code": 200}
2025-07-14 18:47:54 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 36.8, "status_code": 200}
2025-07-14 18:47:54 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 46.85, "status_code": 200}
2025-07-14 18:48:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.6, "status_code": 200}
2025-07-14 18:48:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.03, "status_code": 200}
2025-07-14 18:48:45 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "DELETE", "path": "/users/17524878600144387", "process_time_ms": 135.72, "status_code": 200}
2025-07-14 18:48:45 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.34, "status_code": 200}
2025-07-14 19:03:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 314.88, "status_code": 200}
2025-07-14 19:03:43 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 362.94, "status_code": 200}
2025-07-14 19:03:47 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 317.62, "status_code": 200}
2025-07-14 19:03:51 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 238.49, "status_code": 200}
2025-07-14 19:03:55 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 1.83, "status_code": 422}
2025-07-14 19:03:59 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 9.56, "status_code": 200}
2025-07-14 19:05:09 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 346.27, "status_code": 200}
2025-07-14 19:05:14 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 243.75, "status_code": 200}
2025-07-14 19:05:18 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.17, "status_code": 200}
2025-07-14 19:19:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 278.86, "status_code": 200}
2025-07-14 19:19:08 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 27.77, "status_code": 200}
2025-07-14 19:20:46 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 17.92, "status_code": 200}
2025-07-14 19:20:46 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 20.68, "status_code": 200}
2025-07-14 19:21:12 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 13.13, "status_code": 200}
2025-07-14 19:21:13 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 18.82, "status_code": 200}
2025-07-14 19:21:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.0, "status_code": 200}
2025-07-14 19:21:26 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 3.44, "status_code": 200}
2025-07-14 19:21:49 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.05, "status_code": 200}
2025-07-14 19:21:49 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 4.37, "status_code": 200}
2025-07-14 19:22:01 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.03, "status_code": 200}
2025-07-14 19:22:01 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/organizations/list", "process_time_ms": 3.25, "status_code": 200}
2025-07-14 19:26:36 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.72, "status_code": 200}
2025-07-14 19:26:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.81, "status_code": 200}
2025-07-14 19:26:38 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.86, "status_code": 200}
2025-07-14 19:26:39 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.26, "status_code": 200}
2025-07-14 19:26:45 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 29.25, "status_code": 200}
2025-07-14 19:26:52 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.72, "status_code": 200}
2025-07-14 19:26:54 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.32, "status_code": 200}
2025-07-14 19:26:54 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.73, "status_code": 200}
2025-07-14 19:27:16 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 24.4, "status_code": 200}
2025-07-14 19:27:16 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.83, "status_code": 200}
2025-07-14 19:27:20 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 14.92, "status_code": 200}
2025-07-14 19:27:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.4, "status_code": 200}
2025-07-14 19:28:42 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 16.21, "status_code": 200}
2025-07-14 19:28:44 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 11.72, "status_code": 200}
2025-07-14 19:28:57 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 1437.41, "status_code": 200}
2025-07-14 19:29:01 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 432.36, "status_code": 200}
2025-07-14 19:29:05 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 321.0, "status_code": 200}
2025-07-14 19:29:06 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "PUT", "path": "/users/17524925164951675", "process_time_ms": 347.46, "status_code": 200}
2025-07-14 19:29:06 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.49, "status_code": 200}
2025-07-14 19:29:10 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 331.15, "status_code": 200}
2025-07-14 19:29:14 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 367.44, "status_code": 200}
2025-07-14 19:29:18 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 4.33, "status_code": 200}
2025-07-14 19:29:22 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 342.22, "status_code": 200}
2025-07-14 19:29:25 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "PUT", "path": "/users/17524925361491496", "process_time_ms": 340.25, "status_code": 200}
2025-07-14 19:29:25 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.55, "status_code": 200}
2025-07-14 19:29:27 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 344.28, "status_code": 200}
2025-07-14 19:29:29 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.32, "status_code": 200}
2025-07-14 19:29:30 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.42, "status_code": 200}
2025-07-14 19:29:31 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.19, "status_code": 200}
2025-07-14 19:29:31 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "POST", "path": "/users/create", "process_time_ms": 334.9, "status_code": 200}
2025-07-14 19:29:31 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.07, "status_code": 200}
2025-07-14 19:29:32 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.28, "status_code": 200}
2025-07-14 19:29:32 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.82, "status_code": 200}
2025-07-14 19:29:32 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.86, "status_code": 200}
2025-07-14 19:29:32 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.99, "status_code": 200}
2025-07-14 19:29:33 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.7, "status_code": 200}
2025-07-14 19:29:33 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.99, "status_code": 200}
2025-07-14 19:29:33 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.28, "status_code": 200}
2025-07-14 19:29:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.32, "status_code": 200}
2025-07-14 19:29:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.19, "status_code": 200}
2025-07-14 19:29:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.91, "status_code": 200}
2025-07-14 19:29:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 7.12, "status_code": 200}
2025-07-14 19:29:34 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.64, "status_code": 200}
2025-07-14 19:29:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.79, "status_code": 200}
2025-07-14 19:29:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.87, "status_code": 200}
2025-07-14 19:29:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.41, "status_code": 200}
2025-07-14 19:29:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 6.59, "status_code": 200}
2025-07-14 19:29:35 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.49, "status_code": 200}
2025-07-14 19:29:36 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 10.0, "status_code": 200}
2025-07-14 19:29:36 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.7, "status_code": 200}
2025-07-14 19:29:36 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.09, "status_code": 200}
2025-07-14 19:29:36 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.66, "status_code": 200}
2025-07-14 19:29:36 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.5, "status_code": 200}
2025-07-14 19:29:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.96, "status_code": 200}
2025-07-14 19:29:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.95, "status_code": 200}
2025-07-14 19:29:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.06, "status_code": 200}
2025-07-14 19:29:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 3.61, "status_code": 200}
2025-07-14 19:29:37 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 5.69, "status_code": 200}
2025-07-14 19:29:40 | performance | INFO | logging_middleware.py:210 | Performance: {"method": "GET", "path": "/users/list", "process_time_ms": 4.52, "status_code": 200}
