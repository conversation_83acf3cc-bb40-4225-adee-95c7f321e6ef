#!/usr/bin/env python3
"""
最终测试退出接口
"""
import urllib.request
import urllib.parse
import json

def test_logout_without_token():
    """测试不带token的退出接口"""
    print("=== 测试不带token的退出接口 ===")
    url = "http://127.0.0.1:8000/users/logout"
    
    # 创建POST请求（不带Authorization header）
    req = urllib.request.Request(url, data=b'', method='POST')
    req.add_header('Content-Type', 'application/json')
    
    try:
        with urllib.request.urlopen(req) as response:
            response_data = response.read().decode('utf-8')
            print(f"状态码: {response.status}")
            print(f"响应: {response_data}")
            
    except urllib.error.HTTPError as e:
        error_data = e.read().decode('utf-8')
        print(f"HTTP错误 {e.code}: {error_data}")
        return e.code == 401  # 期望返回401

def test_logout_with_valid_token():
    """测试带有效token的退出接口"""
    print("\n=== 测试带有效token的退出接口 ===")
    
    # 首先登录获取token
    login_url = "http://127.0.0.1:8000/users/login"
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    json_data = json.dumps(login_data).encode('utf-8')
    req = urllib.request.Request(login_url, data=json_data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        with urllib.request.urlopen(req) as response:
            response_data = response.read().decode('utf-8')
            login_response = json.loads(response_data)
            
            if login_response.get('code') == 200:
                token = login_response['data']['access_token']
                print(f"登录成功，获取到token")
                
                # 使用token调用退出接口
                logout_url = "http://127.0.0.1:8000/users/logout"
                logout_req = urllib.request.Request(logout_url, data=b'', method='POST')
                logout_req.add_header('Content-Type', 'application/json')
                logout_req.add_header('Authorization', f'Bearer {token}')
                
                with urllib.request.urlopen(logout_req) as logout_response:
                    logout_data = logout_response.read().decode('utf-8')
                    print(f"退出接口状态码: {logout_response.status}")
                    
                    logout_json = json.loads(logout_data)
                    print(f"退出接口响应: {json.dumps(logout_json, indent=2, ensure_ascii=False)}")
                    
                    # 验证响应
                    if logout_json.get('code') == 200 and logout_json.get('data', {}).get('username') == 'admin':
                        print("✅ 带token的退出测试通过")
                        return True
                    else:
                        print("❌ 带token的退出测试失败")
                        return False
            else:
                print(f"登录失败: {login_response}")
                return False
                
    except Exception as e:
        print(f"测试带token退出时出错: {e}")
        return False

def test_logout_with_invalid_token():
    """测试带无效token的退出接口"""
    print("\n=== 测试带无效token的退出接口 ===")
    
    logout_url = "http://127.0.0.1:8000/users/logout"
    logout_req = urllib.request.Request(logout_url, data=b'', method='POST')
    logout_req.add_header('Content-Type', 'application/json')
    logout_req.add_header('Authorization', 'Bearer invalid_token_here')
    
    try:
        with urllib.request.urlopen(logout_req) as logout_response:
            logout_data = logout_response.read().decode('utf-8')
            print(f"退出接口状态码: {logout_response.status}")
            
            logout_json = json.loads(logout_data)
            print(f"退出接口响应: {json.dumps(logout_json, indent=2, ensure_ascii=False)}")
            
            # 验证响应（应该仍然允许退出，但data为null）
            if logout_json.get('code') == 200 and logout_json.get('data') is None:
                print("✅ 带无效token的退出测试通过（允许退出）")
                return True
            else:
                print("❌ 带无效token的退出测试失败")
                return False
                
    except Exception as e:
        print(f"测试带无效token退出时出错: {e}")
        return False

if __name__ == "__main__":
    print("开始退出接口完整测试...")
    
    # 测试1：不带token
    test1_passed = test_logout_without_token()
    
    # 测试2：带有效token
    test2_passed = test_logout_with_valid_token()
    
    # 测试3：带无效token
    test3_passed = test_logout_with_invalid_token()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"不带token的退出: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"带有效token的退出: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"带无效token的退出: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有退出接口测试都通过了！")
    else:
        print("\n⚠️  部分测试失败，请检查实现")
