#!/usr/bin/env python3
"""
检查数据库表结构
"""

import sqlite3
import os

def check_database_structure():
    """检查数据库表结构"""
    db_path = "project_management.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"数据库中的表: {[table[0] for table in tables]}")
        
        # 检查用户表结构
        if ('users',) in tables:
            cursor.execute("PRAGMA table_info(users)")
            columns = cursor.fetchall()
            print(f"\n用户表结构:")
            for column in columns:
                print(f"  {column[1]} ({column[2]}) - 主键: {column[5]}, 非空: {column[3]}, 默认值: {column[4]}")
        else:
            print("❌ 用户表不存在")
        
        # 检查组织表结构
        if ('organizations',) in tables:
            cursor.execute("PRAGMA table_info(organizations)")
            columns = cursor.fetchall()
            print(f"\n组织表结构:")
            for column in columns:
                print(f"  {column[1]} ({column[2]}) - 主键: {column[5]}, 非空: {column[3]}, 默认值: {column[4]}")
        else:
            print("❌ 组织表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")

if __name__ == "__main__":
    check_database_structure()
