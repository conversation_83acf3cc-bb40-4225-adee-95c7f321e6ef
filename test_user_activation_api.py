#!/usr/bin/env python3
"""
测试用户激活/禁用API接口
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def get_test_user_ids():
    """获取测试用户ID"""
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                return [user["id"] for user in users if user["username"] != "admin"]  # 排除admin用户
        return []
    except Exception as e:
        print(f"❌ 获取用户列表失败: {e}")
        return []

def test_single_user_status_update():
    """测试单个用户状态更新"""
    print("\n=== 测试单个用户状态更新 ===")
    
    user_ids = get_test_user_ids()
    if not user_ids:
        print("❌ 没有找到测试用户")
        return
    
    test_user_id = user_ids[0]
    print(f"测试用户ID: {test_user_id}")
    
    # 测试禁用用户
    print("\n1. 测试禁用用户")
    try:
        payload = {"status": "inactive"}
        response = requests.put(f"{BASE_URL}/users/{test_user_id}/status", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 用户禁用成功")
                user_data = result.get("data", {})
                print(f"  用户状态: {user_data.get('status')}")
            else:
                print(f"❌ 禁用失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 测试激活用户
    print("\n2. 测试激活用户")
    try:
        payload = {"status": "active"}
        response = requests.put(f"{BASE_URL}/users/{test_user_id}/status", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 用户激活成功")
                user_data = result.get("data", {})
                print(f"  用户状态: {user_data.get('status')}")
            else:
                print(f"❌ 激活失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_batch_activate_users():
    """测试批量激活用户"""
    print("\n=== 测试批量激活用户 ===")
    
    user_ids = get_test_user_ids()
    if len(user_ids) < 2:
        print("❌ 测试用户数量不足")
        return
    
    test_user_ids = user_ids[:2]  # 取前两个用户
    print(f"测试用户IDs: {test_user_ids}")
    
    try:
        payload = {"user_ids": test_user_ids}
        response = requests.post(f"{BASE_URL}/users/activate", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 批量激活成功")
                data = result.get("data", {})
                print(f"  更新数量: {data.get('updated_count')}")
                print(f"  更新用户: {data.get('updated_users')}")
            else:
                print(f"❌ 批量激活失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_batch_deactivate_users():
    """测试批量禁用用户"""
    print("\n=== 测试批量禁用用户 ===")
    
    user_ids = get_test_user_ids()
    if len(user_ids) < 2:
        print("❌ 测试用户数量不足")
        return
    
    test_user_ids = user_ids[:2]  # 取前两个用户
    print(f"测试用户IDs: {test_user_ids}")
    
    try:
        payload = {"user_ids": test_user_ids}
        response = requests.post(f"{BASE_URL}/users/deactivate", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                print("✅ 批量禁用成功")
                data = result.get("data", {})
                print(f"  更新数量: {data.get('updated_count')}")
                print(f"  更新用户: {data.get('updated_users')}")
            else:
                print(f"❌ 批量禁用失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_invalid_user_status_update():
    """测试无效用户状态更新"""
    print("\n=== 测试无效用户状态更新 ===")
    
    # 测试不存在的用户ID
    invalid_user_id = "invalid_user_id_123"
    
    try:
        payload = {"status": "active"}
        response = requests.put(f"{BASE_URL}/users/{invalid_user_id}/status", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 404:
                print("✅ 正确处理了不存在的用户")
                print(f"  错误信息: {result.get('msg')}")
            else:
                print(f"❌ 未正确处理不存在的用户: {result}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def verify_user_status_changes():
    """验证用户状态变更"""
    print("\n=== 验证用户状态变更 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("data", {}).get("items", [])
                print("当前用户状态:")
                for user in users:
                    print(f"  - {user['name']} ({user['username']}): {user['status']}")
            else:
                print(f"❌ 获取用户列表失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """运行所有测试"""
    print("开始测试用户激活/禁用API...")
    
    # 显示初始状态
    verify_user_status_changes()
    
    # 测试单个用户状态更新
    test_single_user_status_update()
    
    # 测试批量操作
    test_batch_deactivate_users()
    test_batch_activate_users()
    
    # 测试错误处理
    test_invalid_user_status_update()
    
    # 显示最终状态
    verify_user_status_changes()
    
    print("\n=== 用户激活/禁用API测试完成 ===")

if __name__ == "__main__":
    main()
