#!/usr/bin/env python3
"""
测试新的用户列表API接口
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_user_list_basic():
    """测试基本的用户列表查询"""
    print("\n=== 测试基本用户列表查询 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/users/list")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 200:
                data = result.get("data", {})
                items = data.get("items", [])
                pagination = data.get("pagination", {})
                
                print(f"✅ 用户列表查询成功")
                print(f"  用户数量: {len(items)}")
                print(f"  分页信息: {pagination}")
                
                if items:
                    print(f"  第一个用户: {items[0]}")
            else:
                print(f"❌ API返回错误: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_user_list_with_pagination():
    """测试分页功能"""
    print("\n=== 测试分页功能 ===")
    
    params = {
        "page": 1,
        "pageSize": 5
    }
    
    try:
        response = requests.get(f"{BASE_URL}/users/list", params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                items = data.get("items", [])
                pagination = data.get("pagination", {})
                
                print(f"✅ 分页查询成功")
                print(f"  请求参数: {params}")
                print(f"  返回用户数: {len(items)}")
                print(f"  分页信息: {pagination}")
            else:
                print(f"❌ API返回错误: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_user_list_with_search():
    """测试搜索功能"""
    print("\n=== 测试搜索功能 ===")
    
    params = {
        "search": "test",
        "page": 1,
        "pageSize": 10
    }
    
    try:
        response = requests.get(f"{BASE_URL}/users/list", params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                items = data.get("items", [])
                
                print(f"✅ 搜索查询成功")
                print(f"  搜索关键词: {params['search']}")
                print(f"  匹配用户数: {len(items)}")
                
                for user in items:
                    print(f"  - {user.get('username')} (ID: {user.get('id')})")
            else:
                print(f"❌ API返回错误: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_user_list_with_filters():
    """测试筛选功能"""
    print("\n=== 测试筛选功能 ===")
    
    # 测试角色筛选
    params = {
        "role": "admin",
        "page": 1,
        "pageSize": 10
    }
    
    try:
        response = requests.get(f"{BASE_URL}/users/list", params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                items = data.get("items", [])
                
                print(f"✅ 角色筛选查询成功")
                print(f"  筛选角色: {params['role']}")
                print(f"  匹配用户数: {len(items)}")
                
                for user in items:
                    print(f"  - {user.get('username')} (角色: {user.get('role')})")
            else:
                print(f"❌ API返回错误: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_user_list_with_status_filter():
    """测试状态筛选"""
    print("\n=== 测试状态筛选 ===")

    # 测试inactive状态筛选
    params = {
        "status": "inactive",
        "page": 1,
        "pageSize": 10
    }

    try:
        response = requests.get(f"{BASE_URL}/users/list", params=params)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                items = data.get("items", [])

                print(f"✅ 状态筛选查询成功")
                print(f"  筛选状态: {params['status']}")
                print(f"  匹配用户数: {len(items)}")

                for user in items:
                    print(f"  - {user.get('username')} (状态: {user.get('status')})")
            else:
                print(f"❌ API返回错误: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_user_list_combined_filters():
    """测试组合筛选"""
    print("\n=== 测试组合筛选 ===")

    params = {
        "role": "admin",
        "status": "inactive",
        "page": 1,
        "pageSize": 10
    }

    try:
        response = requests.get(f"{BASE_URL}/users/list", params=params)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                items = data.get("items", [])

                print(f"✅ 组合筛选查询成功")
                print(f"  筛选条件: {params}")
                print(f"  匹配用户数: {len(items)}")

                for user in items:
                    print(f"  - {user.get('username')} (角色: {user.get('role')}, 状态: {user.get('status')})")
            else:
                print(f"❌ API返回错误: {result.get('msg')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """运行所有测试"""
    print("开始测试新的用户列表API...")

    test_user_list_basic()
    test_user_list_with_pagination()
    test_user_list_with_search()
    test_user_list_with_filters()
    test_user_list_with_status_filter()
    test_user_list_combined_filters()

    print("\n=== 用户列表API测试完成 ===")

if __name__ == "__main__":
    main()
